.echo "=== Manual ASAR Analysis Started ==="
.logopen C:\Analysis\Logs\manual_asar_analysis.log
.ttime

.echo "=== Setting up ASAR breakpoints ==="
bp claude+0x3AC730 ".echo 'ASAR: IsEmbeddedAsarIntegrityValidationEnabled'; r rax; k 3; g"
bp claude+0x3A99C0 ".echo 'ASAR: GetIntegrityForAsarArchive'; r rcx,rdx,r8; k 3; g"
bp claude+0x429040 ".echo 'ASAR: GetIntegrityFromExecutableResources'; r rcx,rdx; k 3; g"
bp claude+0x337580 ".echo 'ASAR: ValidateAsarBlockHash'; r rcx,rdx,r8; k 3; g"
bp claude+0x337790 ".echo 'ASAR: AsarFileValidator'; r rcx,rdx; k 3; g"

.echo "=== Setting up API breakpoints ==="
bp kernel32!FindResourceW ".echo 'API: FindResourceW'; .if (@rdx != 0) { du @rdx } .if (@r8 != 0) { du @r8 } g"
bp kernel32!LoadResource ".echo 'API: LoadResource'; r rcx,rdx; g"
bp kernel32!CreateFileW ".echo 'API: CreateFileW'; .if (@rcx != 0) { du @rcx } g"

.echo "=== Setting up crypto breakpoints ==="
bp bcrypt!BCryptCreateHash ".echo 'CRYPTO: BCryptCreateHash'; r rcx,rdx; g"
bp bcrypt!BCryptHashData ".echo 'CRYPTO: BCryptHashData'; r rcx,rdx,r8; g"
bp bcrypt!BCryptFinishHash ".echo 'CRYPTO: BCryptFinishHash'; r rcx,rdx,r8; g"

.echo "=== Searching for ASAR strings ==="
s -a 0 L?0x7fffffff "app.asar"
s -a 0 L?0x7fffffff "ElectronAsar"
s -a 0 L?0x7fffffff "Integrity"
s -a 0 L?0x7fffffff "sha256"

.echo "=== Manual setup complete - use 'g' to start monitoring ==="
.echo "=== Go to VM and run: C:\Tools\claude.exe ==="
.echo "=== Watch for ASAR:, API:, and CRYPTO: messages ==="
