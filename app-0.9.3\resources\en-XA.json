{"+/cwsayrqk": [{"type": 0, "value": "["}, {"type": 0, "value": "Ȧƈŧŭŭȧȧŀ Şīẑḗḗ"}, {"type": 0, "value": "]"}], "+7sd9hoyZA": [{"type": 0, "value": "["}, {"type": 0, "value": "Ƈǿǿƥẏ"}, {"type": 0, "value": "]"}], "/PgA81GVOD": [{"type": 0, "value": "["}, {"type": 0, "value": "Ḗḓīŧ"}, {"type": 0, "value": "]"}], "/bRGKhnXQ6": [{"type": 0, "value": "["}, {"type": 0, "value": "Ȧ ƞḗḗẇ ṽḗḗřşīǿǿƞ īş ȧȧṽȧȧīŀȧȧƀŀḗḗ. Īŧ ẇīŀŀ ƀḗḗ ḓǿǿẇƞŀǿǿȧȧḓḗḗḓ ȧȧƞḓ īƞşŧȧȧŀŀḗḗḓ ȧȧŭŭŧǿǿḿȧȧŧīƈȧȧŀŀẏ."}, {"type": 0, "value": "]"}], "0g8/VVdNuN": [{"type": 0, "value": "["}, {"type": 0, "value": "Ɠǿǿ ŧǿǿ ƈŀȧȧŭŭḓḗḗ.ȧȧī/şḗḗŧŧīƞɠş ŧǿǿ ƈǿǿƞƒīɠŭŭřḗḗ ẏǿǿŭŭř ƥřǿǿƒīŀḗḗ, ŧḗḗȧȧḿ, ȧȧƞḓ ḿǿǿřḗḗ."}, {"type": 0, "value": "]"}], "0tZLEYF8mJ": [{"type": 0, "value": "["}, {"type": 0, "value": "Ḓḗḗṽḗḗŀǿǿƥḗḗř"}, {"type": 0, "value": "]"}], "1HUTYwndT2": [{"type": 0, "value": "["}, {"type": 0, "value": "Ẇīƞḓǿǿẇ"}, {"type": 0, "value": "]"}], "1PfZLi/OV7": [{"type": 0, "value": "["}, {"type": 0, "value": "Ɠḗḗƞḗḗřȧȧŀ"}, {"type": 0, "value": "]"}], "1TJUzU26sO": [{"type": 0, "value": "["}, {"type": 0, "value": "Şŭŭƀḿīŧ Ƒḗḗḗḗḓƀȧȧƈķ…"}, {"type": 0, "value": "]"}], "25aCMlTDUq": [{"type": 0, "value": "["}, {"type": 0, "value": "Ȧŭŭŧǿǿḿȧȧŧīƈȧȧŀŀẏ şŧȧȧřŧ Ƈŀȧȧŭŭḓḗḗ ẇħḗḗƞ ẏǿǿŭŭ ŀǿǿɠ īƞ"}, {"type": 0, "value": "]"}], "3ML3xT+gEV": [{"type": 0, "value": "["}, {"type": 0, "value": "Řḗḗḓǿǿ"}, {"type": 0, "value": "]"}], "3gG1j3kRBX": [{"type": 0, "value": "["}, {"type": 0, "value": "Şŭŭƀḿīŧ Ƒḗḗḗḗḓƀȧȧƈķ..."}, {"type": 0, "value": "]"}], "3unrKzH4zB": [{"type": 0, "value": "["}, {"type": 0, "value": "Ƈǿǿƥẏ"}, {"type": 0, "value": "]"}], "4qP7MjrQfC": [{"type": 0, "value": "["}, {"type": 0, "value": "Ḗƞṽīřǿǿƞḿḗḗƞŧ ṽȧȧřīȧȧƀŀḗḗş"}, {"type": 0, "value": "]"}], "5DUIVR3fVi": [{"type": 0, "value": "["}, {"type": 0, "value": "Ȧƀǿǿŭŭŧ..."}, {"type": 0, "value": "]"}], "6yv8ytK4El": [{"type": 0, "value": "["}, {"type": 0, "value": "Ƈħḗḗƈķ ẏǿǿŭŭř ƞḗḗŧẇǿǿřķ ƈǿǿƞƞḗḗƈŧīǿǿƞ"}, {"type": 0, "value": "]"}], "7fdcqxofEs": [{"type": 0, "value": "["}, {"type": 0, "value": "Ḗẋīŧ"}, {"type": 0, "value": "]"}], "7gSC+rZzXX": [{"type": 0, "value": "["}, {"type": 0, "value": "Ɋŭŭīƈķŀẏ ǿǿƥḗḗƞ Ƈŀȧȧŭŭḓḗḗ ƒřǿǿḿ ȧȧƞẏẇħḗḗřḗḗ"}, {"type": 0, "value": "]"}], "8YQEOfuaGO": [{"type": 0, "value": "["}, {"type": 0, "value": "Şḗḗŀḗḗƈŧ Ȧŀŀ"}, {"type": 0, "value": "]"}], "9+afSO9e/t": [{"type": 0, "value": "["}, {"type": 0, "value": "Ƒȧȧīŀḗḗḓ ŧǿǿ ƈħḗḗƈķ ƒǿǿř ŭŭƥḓȧȧŧḗḗş: "}, {"type": 1, "value": "error"}, {"type": 0, "value": "]"}], "9uNxNtcrFI": [{"type": 0, "value": "["}, {"type": 0, "value": "Īƞşŧȧȧŀŀ"}, {"type": 0, "value": "]"}], "CZwl8X2D85": [{"type": 0, "value": "["}, {"type": 0, "value": "Ȧḓṽȧȧƞƈḗḗḓ ǿǿƥŧīǿǿƞş"}, {"type": 0, "value": "]"}], "CizRPROPWo": [{"type": 0, "value": "["}, {"type": 0, "value": "Ƈŀȧȧŭŭḓḗḗ Ħḗḗŀƥ"}, {"type": 0, "value": "]"}], "D43DeqP+2t": [{"type": 0, "value": "["}, {"type": 0, "value": "Ƈŀȧȧŭŭḓḗḗ Şḗḗŧŧīƞɠş"}, {"type": 0, "value": "]"}], "D4DyT6MmPy": [{"type": 0, "value": "["}, {"type": 0, "value": "Ƈǿǿŭŭŀḓ ƞǿǿŧ ŀǿǿȧȧḓ ȧȧƥƥ şḗḗŧŧīƞɠş"}, {"type": 0, "value": "]"}], "DQTgg21B7g": [{"type": 0, "value": "["}, {"type": 0, "value": "Şħǿǿẇ Ȧƥƥ"}, {"type": 0, "value": "]"}], "E9jYTa7AbX": [{"type": 0, "value": "["}, {"type": 0, "value": "Şẏşŧḗḗḿ Ŧřȧȧẏ"}, {"type": 0, "value": "]"}], "EfdnINFnIz": [{"type": 0, "value": "["}, {"type": 0, "value": "Ƒīŀḗḗ"}, {"type": 0, "value": "]"}], "GSG5S0ysrR": [{"type": 0, "value": "["}, {"type": 0, "value": "Řŭŭƞ ǿǿƞ Şŧȧȧřŧŭŭƥ"}, {"type": 0, "value": "]"}], "HeHYq6bbS2": [{"type": 0, "value": "["}, {"type": 0, "value": "Ƈŀȧȧŭŭḓḗḗ ƈȧȧƞ řḗḗƈḗḗīṽḗḗ īƞƒǿǿřḿȧȧŧīǿǿƞ ŀīķḗḗ ƥřǿǿḿƥŧş ȧȧƞḓ ȧȧŧŧȧȧƈħḿḗḗƞŧş ƒřǿǿḿ şƥḗḗƈīȧȧŀīẑḗḗḓ şḗḗřṽḗḗřş ŭŭşīƞɠ Ḿǿǿḓḗḗŀ Ƈǿǿƞŧḗḗẋŧ Ƥřǿǿŧǿǿƈǿǿŀ."}, {"type": 0, "value": "]"}], "I5O68ogAtr": [{"type": 0, "value": "["}, {"type": 0, "value": "Ɠḗḗŧ Şŧȧȧřŧḗḗḓ"}, {"type": 0, "value": "]"}], "JVwNvMZjVT": [{"type": 0, "value": "["}, {"type": 0, "value": "Ƥȧȧşŧḗḗ"}, {"type": 0, "value": "]"}], "KAo3lt5Hv+": [{"type": 0, "value": "["}, {"type": 0, "value": "Ƥȧȧşŧḗḗ"}, {"type": 0, "value": "]"}], "Ko/2Ml7mZG": [{"type": 0, "value": "["}, {"type": 0, "value": "Řḗḗŀǿǿȧȧḓ Ŧħīş Ƥȧȧɠḗḗ"}, {"type": 0, "value": "]"}], "L32WRR6NOL": [{"type": 0, "value": "["}, {"type": 0, "value": "Ḓḗḗŀḗḗŧḗḗ"}, {"type": 0, "value": "]"}], "L717supPIA": [{"type": 0, "value": "["}, {"type": 0, "value": "Şḗḗŧŧīƞɠş"}, {"type": 0, "value": "]"}], "LCWUQ/4Fu6": [{"type": 0, "value": "["}, {"type": 0, "value": "Ṽīḗḗẇ"}, {"type": 0, "value": "]"}], "NZIwKxgxJ+": [{"type": 0, "value": "["}, {"type": 0, "value": "Ȧřḗḗ ẏǿǿŭŭ şŭŭřḗḗ ẏǿǿŭŭ ẇȧȧƞŧ ŧǿǿ řḗḗḿǿǿṽḗḗ ŧħḗḗ ḾƇƤ şḗḗřṽḗḗř \""}, {"type": 1, "value": "server<PERSON>ey"}, {"type": 0, "value": "\"?"}, {"type": 0, "value": "]"}], "Nmvo1ufAY5": [{"type": 0, "value": "["}, {"type": 0, "value": "Ƈǿǿŭŭŀḓƞ'ŧ ƈǿǿƞƞḗḗƈŧ ŧǿǿ Ƈŀȧȧŭŭḓḗḗ"}, {"type": 0, "value": "]"}], "O3rtEd7aMd": [{"type": 0, "value": "["}, {"type": 0, "value": "Ƒīƞḓ"}, {"type": 0, "value": "]"}], "ODySlGptaj": [{"type": 0, "value": "["}, {"type": 0, "value": "Şḗḗŧŧīƞɠş…"}, {"type": 0, "value": "]"}], "PH29MShDiy": [{"type": 0, "value": "["}, {"type": 0, "value": "Ƒǿǿřẇȧȧřḓ"}, {"type": 0, "value": "]"}], "PW5U8NgTto": [{"type": 0, "value": "["}, {"type": 0, "value": "Ǿƥḗḗƞ ḾƇƤ Ŀǿǿɠ Ƒīŀḗḗ..."}, {"type": 0, "value": "]"}], "PZtcoAOSsa": [{"type": 0, "value": "["}, {"type": 0, "value": "Ḓǿǿƞ'ŧ Ḗƞȧȧƀŀḗḗ"}, {"type": 0, "value": "]"}], "PbJ4jR0kv1": [{"type": 0, "value": "["}, {"type": 0, "value": "Ẏǿǿŭŭ ȧȧřḗḗ řŭŭƞƞīƞɠ ŧħḗḗ ŀȧȧŧḗḗşŧ ṽḗḗřşīǿǿƞ."}, {"type": 0, "value": "]"}], "RTg057HE1D": [{"type": 0, "value": "["}, {"type": 0, "value": "Şħǿǿẇ Ḓḗḗṽ Ŧǿǿǿǿŀş"}, {"type": 0, "value": "]"}], "S3MXlbjkax": [{"type": 0, "value": "["}, {"type": 0, "value": "Ẇħȧȧŧ ƈȧȧƞ Ī ħḗḗŀƥ ẏǿǿŭŭ ẇīŧħ ŧǿǿḓȧȧẏ?"}, {"type": 0, "value": "]"}], "S3k5yXss2r": [{"type": 0, "value": "["}, {"type": 0, "value": "Ṽḗḗřşīǿǿƞ "}, {"type": 1, "value": "version"}, {"type": 0, "value": "]"}], "TH+W2Ad73P": [{"type": 0, "value": "["}, {"type": 0, "value": "Ƈŭŭŧ"}, {"type": 0, "value": "]"}], "UJCjEVPX6Q": [{"type": 0, "value": "["}, {"type": 0, "value": "Ŀǿǿǿǿķ Ŭƥ"}, {"type": 0, "value": "]"}], "Vvus2ifAny": [{"type": 0, "value": "["}, {"type": 0, "value": "Ḗḓīŧ Ƈǿǿƞƒīɠ"}, {"type": 0, "value": "]"}], "W1pELwt/+a": [{"type": 0, "value": "["}, {"type": 0, "value": "Ƈŀȧȧŭŭḓḗḗ řŭŭƞş īƞ ŧħḗḗ Ƞǿǿŧīƒīƈȧȧŧīǿǿƞ Ȧřḗḗȧȧ"}, {"type": 0, "value": "]"}], "WBvq3HlPae": [{"type": 0, "value": "["}, {"type": 0, "value": "Şḗḗŧ şħǿǿřŧƈŭŭŧ"}, {"type": 0, "value": "]"}], "WF1HSu0jAC": [{"type": 0, "value": "["}, {"type": 0, "value": "Ǿƥḗḗƞ Ŀǿǿɠş Ƒǿǿŀḓḗḗř"}, {"type": 0, "value": "]"}], "WZe86KSdrM": [{"type": 0, "value": "["}, {"type": 0, "value": "Ȧḓḓ ŧǿǿ ḓīƈŧīǿǿƞȧȧřẏ"}, {"type": 0, "value": "]"}], "WlhIx7DfFO": [{"type": 0, "value": "["}, {"type": 0, "value": "ǾĶ"}, {"type": 0, "value": "]"}], "XPIoFTkh3e": [{"type": 0, "value": "["}, {"type": 0, "value": "Ƞǿǿ Ŭƥḓȧȧŧḗḗ Ȧṽȧȧīŀȧȧƀŀḗḗ"}, {"type": 0, "value": "]"}], "XZ36+EBE5/": [{"type": 0, "value": "["}, {"type": 0, "value": "Ẑǿǿǿǿḿ Ǿŭŭŧ"}, {"type": 0, "value": "]"}], "XinCguXCgN": [{"type": 0, "value": "["}, {"type": 0, "value": "Ŀḗḗȧȧřƞ ḿǿǿřḗḗ"}, {"type": 0, "value": "]"}], "YTdYCYAf/Z": [{"type": 0, "value": "["}, {"type": 0, "value": "Şħǿǿẇ Ƈŀȧȧŭŭḓḗḗ īƞ ŧħḗḗ ḿḗḗƞŭŭ ƀȧȧř"}, {"type": 0, "value": "]"}], "Z9g5m/V9Nq": [{"type": 0, "value": "["}, {"type": 0, "value": "Ẑǿǿǿǿḿ Īƞ"}, {"type": 0, "value": "]"}], "ZJZN1+KyJw": [{"type": 0, "value": "["}, {"type": 0, "value": "Şḗḗŧŧīƞɠş..."}, {"type": 0, "value": "]"}], "aNmxuDcWaU": [{"type": 0, "value": "["}, {"type": 0, "value": "Ḗřřǿǿř"}, {"type": 0, "value": "]"}], "aXdFLiVzjd": [{"type": 0, "value": "["}, {"type": 0, "value": "Şħǿǿẇ Ḿȧȧīƞ Ẇīƞḓǿǿẇ"}, {"type": 0, "value": "]"}], "arbRxbtBkP": [{"type": 0, "value": "["}, {"type": 0, "value": "Ɓȧȧƈķ"}, {"type": 0, "value": "]"}], "baGq3gy8z1": [{"type": 0, "value": "["}, {"type": 0, "value": "Ƞḗḗẇ Ƈǿǿƞṽḗḗřşȧȧŧīǿǿƞ"}, {"type": 0, "value": "]"}], "dKX0bpR+a2": [{"type": 0, "value": "["}, {"type": 0, "value": "Ɋŭŭīŧ"}, {"type": 0, "value": "]"}], "dLyz0Srosd": [{"type": 0, "value": "["}, {"type": 0, "value": "Ḓḗḗṽḗḗŀǿǿƥḗḗř"}, {"type": 0, "value": "]"}], "fEeEFfSz4K": [{"type": 0, "value": "["}, {"type": 0, "value": "Ẑǿǿǿǿḿ Īƞ (īƞḓīḗḗ ƈǿǿǿǿŀḗḗř ṽḗḗřşīǿǿƞ)"}, {"type": 0, "value": "]"}], "fFJxOwJRj2": [{"type": 0, "value": "["}, {"type": 0, "value": "Ŭƞḓǿǿ"}, {"type": 0, "value": "]"}], "fWDSQQgRO5": [{"type": 0, "value": "["}, {"type": 0, "value": "Ḿḗḗƞŭŭ Ɓȧȧř"}, {"type": 0, "value": "]"}], "iFRmqBsr1N": [{"type": 0, "value": "["}, {"type": 0, "value": "Ȧƀǿǿŭŭŧ Ƈŀȧȧŭŭḓḗḗ"}, {"type": 0, "value": "]"}], "ilE9e0uxNN": [{"type": 0, "value": "["}, {"type": 0, "value": "Řḗḗƒřḗḗşħ"}, {"type": 0, "value": "]"}], "j66cdL4EK5": [{"type": 0, "value": "["}, {"type": 0, "value": "Ǿƥḗḗƞ Ḓǿǿƈŭŭḿḗḗƞŧȧȧŧīǿǿƞ"}, {"type": 0, "value": "]"}], "jd5ZNrRMNP": [{"type": 0, "value": "["}, {"type": 0, "value": "Ẏǿǿŭŭ'řḗḗ ȧȧƞ ḗḗȧȧřŀẏ ḗḗẋƥŀǿǿřḗḗř, şǿǿ ŀḗḗŧ ŭŭş ķƞǿǿẇ ẏǿǿŭŭř ƒḗḗḗḗḓƀȧȧƈķ."}, {"type": 0, "value": "]"}], "k+06oXbIas": [{"type": 0, "value": "["}, {"type": 0, "value": "Şħǿǿẇ Ƈŀȧȧŭŭḓḗḗ īƞ ŧħḗḗ ƞǿǿŧīƒīƈȧȧŧīǿǿƞş ȧȧřḗḗȧȧ"}, {"type": 0, "value": "]"}], "kYwW0OsI4M": [{"type": 0, "value": "["}, {"type": 0, "value": "Ƈŀȧȧŭŭḓḗḗ Ḓḗḗşķŧǿǿƥ īş īƞ ƀḗḗŧȧȧ."}, {"type": 0, "value": "]"}], "m3GfpKD1WX": [{"type": 0, "value": "["}, {"type": 0, "value": "Řḗḗŀǿǿȧȧḓ"}, {"type": 0, "value": "]"}], "mRXjxhS6p4": [{"type": 0, "value": "["}, {"type": 0, "value": "Ƈħḗḗƈķ ƒǿǿř Ŭƥḓȧȧŧḗḗş…"}, {"type": 0, "value": "]"}], "ngLpGT7bUJ": [{"type": 0, "value": "["}, {"type": 0, "value": "Ƈǿǿŭŭŀḓ ƞǿǿŧ ŀǿǿȧȧḓ ḓḗḗṽḗḗŀǿǿƥḗḗř şḗḗŧŧīƞɠş"}, {"type": 0, "value": "]"}], "oQuOiX24pp": [{"type": 0, "value": "["}, {"type": 0, "value": "Ɋŭŭīŧ"}, {"type": 0, "value": "]"}], "pWXxZASpOB": [{"type": 0, "value": "["}, {"type": 0, "value": "Ħḗḗŀƥ"}, {"type": 0, "value": "]"}], "pgaCSv2/6H": [{"type": 0, "value": "["}, {"type": 0, "value": "Ȧřɠŭŭḿḗḗƞŧş"}, {"type": 0, "value": "]"}], "q4hs14B00V": [{"type": 0, "value": "["}, {"type": 0, "value": "Ŭƞķƞǿǿẇƞ ḗḗřřǿǿř"}, {"type": 0, "value": "]"}], "rNAd+HxSK4": [{"type": 0, "value": "["}, {"type": 0, "value": "Ǿƥḗḗƞ ḾƇƤ Ŀǿǿɠ Ƒīŀḗḗ"}, {"type": 0, "value": "]"}], "rY99UXvTDU": [{"type": 0, "value": "["}, {"type": 0, "value": "Ƈǿǿƥẏ Īḿȧȧɠḗḗ"}, {"type": 0, "value": "]"}], "rdiPpQVqvY": [{"type": 0, "value": "["}, {"type": 0, "value": "Ḓḗḗṽḗḗŀǿǿƥḗḗř ḿǿǿḓḗḗ ȧȧŀŀǿǿẇş ȧȧƈƈḗḗşş ŧǿǿ ḓḗḗṽḗḗŀǿǿƥḗḗř ŧǿǿǿǿŀş ȧȧƞḓ ḓḗḗƀŭŭɠɠīƞɠ ƒḗḗȧȧŧŭŭřḗḗş. Ǿƞŀẏ ḗḗƞȧȧƀŀḗḗ ŧħīş īƒ ẏǿǿŭŭ ķƞǿǿẇ ẇħȧȧŧ ẏǿǿŭŭ'řḗḗ ḓǿǿīƞɠ."}, {"type": 0, "value": "]"}], "rwFEudHXey": [{"type": 0, "value": "["}, {"type": 0, "value": "Ŧħḗḗřḗḗ ẇȧȧş ȧȧƞ ḗḗřřǿǿř řḗḗȧȧḓīƞɠ ǿǿř ƥȧȧřşīƞɠ ƈŀȧȧŭŭḓḗḗ_ḓḗḗşķŧǿǿƥ_ƈǿǿƞƒīɠ.ĵşǿǿƞ: "}, {"type": 1, "value": "error"}, {"type": 0, "value": "]"}], "sNnRQsIEYz": [{"type": 0, "value": "["}, {"type": 0, "value": "Ƒīƞḓ īƞ ƥȧȧɠḗḗ"}, {"type": 0, "value": "]"}], "sZxWXq9BzJ": [{"type": 0, "value": "["}, {"type": 0, "value": "Ɠīṽḗḗ ƒḗḗḗḗḓƀȧȧƈķ"}, {"type": 0, "value": "]"}], "sys7RHphmL": [{"type": 0, "value": "["}, {"type": 0, "value": "Ƒḗḗḗḗḓƀȧȧƈķ"}, {"type": 0, "value": "]"}], "tWutslc/9Z": [{"type": 0, "value": "["}, {"type": 0, "value": "Ḗƞȧȧƀŀḗḗ"}, {"type": 0, "value": "]"}], "u1/hT7oRQY": [{"type": 0, "value": "["}, {"type": 0, "value": "Ɋŭŭīƈķ Ḗƞŧřẏ Ķḗḗẏƀǿǿȧȧřḓ Şħǿǿřŧƈŭŭŧ"}, {"type": 0, "value": "]"}], "uc3dnSo+eo": [{"type": 0, "value": "["}, {"type": 0, "value": "Ƒīŀḗḗ"}, {"type": 0, "value": "]"}], "urCd4k/cE0": [{"type": 0, "value": "["}, {"type": 0, "value": "Ƈǿǿḿḿȧȧƞḓ"}, {"type": 0, "value": "]"}], "vgLHPxjh9O": [{"type": 0, "value": "["}, {"type": 0, "value": "Ḗƞȧȧƀŀḗḗ Ḓḗḗṽḗḗŀǿǿƥḗḗř Ḿǿǿḓḗḗ"}, {"type": 0, "value": "]"}], "wS64bVG2CO": [{"type": 0, "value": "["}, {"type": 0, "value": "ḾƇƤ īş ȧȧ ƥřǿǿŧǿǿƈǿǿŀ ŧħȧȧŧ ḗḗƞȧȧƀŀḗḗş şḗḗƈŭŭřḗḗ ƈǿǿƞƞḗḗƈŧīǿǿƞş ƀḗḗŧẇḗḗḗḗƞ ƈŀīḗḗƞŧş, şŭŭƈħ ȧȧş ŧħḗḗ Ƈŀȧȧŭŭḓḗḗ Ḓḗḗşķŧǿǿƥ ȧȧƥƥ, ȧȧƞḓ ŀǿǿƈȧȧŀ şḗḗřṽīƈḗḗş."}, {"type": 0, "value": "]"}], "xJs1jZ8PoA": [{"type": 0, "value": "["}, {"type": 0, "value": "Ƈŀȧȧŭŭḓḗḗ řŭŭƞş īƞ ŧħḗḗ ƀȧȧƈķɠřǿǿŭŭƞḓ ḗḗṽḗḗƞ ẇħḗḗƞ ẏǿǿŭŭ ƈŀǿǿşḗḗ ŧħḗḗ ẇīƞḓǿǿẇ. Ƈŀīƈķ ŧħḗḗ Ƈŀȧȧŭŭḓḗḗ īƈǿǿƞ īƞ ŧħḗḗ ŧřȧȧẏ ŧǿǿ řḗḗǿǿƥḗḗƞ ŧħḗḗ ȧȧƥƥ, ǿǿř řīɠħŧ-ƈŀīƈķ ŧǿǿ ɋŭŭīŧ."}, {"type": 0, "value": "]"}], "xKRKzVVy9c": [{"type": 0, "value": "["}, {"type": 0, "value": "Ƈǿǿƞƒīɠŭŭřḗḗ"}, {"type": 0, "value": "]"}], "xd436TVDRZ": [{"type": 0, "value": "["}, {"type": 0, "value": "Ŧħḗḗřḗḗ ẇȧȧş ȧȧƞ ḗḗřřǿǿř řḗḗȧȧḓīƞɠ ǿǿř ƥȧȧřşīƞɠ ḓḗḗṽḗḗŀǿǿƥḗḗř_şḗḗŧŧīƞɠş.ĵşǿǿƞ: "}, {"type": 1, "value": "error"}, {"type": 0, "value": "]"}], "y9tCbmRzHN": [{"type": 0, "value": "["}, {"type": 0, "value": "Ḗƞȧȧƀŀḗḗ Ḓḗḗṽḗḗŀǿǿƥḗḗř Ḿǿǿḓḗḗ?"}, {"type": 0, "value": "]"}], "ytjMRobdyL": [{"type": 0, "value": "["}, {"type": 0, "value": "Ŭƥḓȧȧŧḗḗ Ȧṽȧȧīŀȧȧƀŀḗḗ"}, {"type": 0, "value": "]"}], "zAYm/Z684h": [{"type": 0, "value": "["}, {"type": 0, "value": "Ħḗḗŀƥ"}, {"type": 0, "value": "]"}], "zCIK9K8J4a": [{"type": 0, "value": "["}, {"type": 0, "value": "Ḗřřǿǿř"}, {"type": 0, "value": "]"}], "zSP70MVzIo": [{"type": 0, "value": "["}, {"type": 0, "value": "Ƈŀḗḗȧȧř şħǿǿřŧƈŭŭŧ"}, {"type": 0, "value": "]"}]}