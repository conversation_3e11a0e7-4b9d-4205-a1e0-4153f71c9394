@echo off
REM Quick ASAR Analysis - Simple automation for immediate results

echo ===============================
echo Quick ASAR Integrity Analysis
echo ===============================

set COM_PIPE=\\.\pipe\com_1
set LOG_DIR=C:\Analysis\Logs

REM Create log directory
if not exist "%LOG_DIR%" mkdir "%LOG_DIR%"

REM Create timestamp (simple version)
set timestamp=%date:~10,4%%date:~4,2%%date:~7,2%_%time:~0,2%%time:~3,2%%time:~6,2%
set timestamp=%timestamp: =0%
set LOG_FILE=%LOG_DIR%\quick_asar_%timestamp%.log

echo Starting quick ASAR analysis...
echo Log file: %LOG_FILE%
echo.

REM Check VM status
echo Checking VM status...
tasklist | findstr vmwp >nul
if errorlevel 1 (
    echo ERROR: KernelDevelopment VM not running!
    echo Please start the VM first.
    pause
    exit /b 1
)
echo VM is running ✓

echo.
echo Starting WinDbg with ASAR monitoring...
echo.

REM Create quick WinDbg script
set QUICK_SCRIPT=%LOG_DIR%\quick_windbg_%timestamp%.txt
(
echo .logopen "%LOG_FILE%"
echo .echo "Quick ASAR Analysis Started"
echo .ttime
echo bp claude+0x3AC730 ".echo 'FOUND: IsEmbeddedAsarIntegrityValidationEnabled called'; r rax; g"
echo bp claude+0x3A99C0 ".echo 'FOUND: GetIntegrityForAsarArchive called'; r rcx,rdx; g"
echo bp kernel32!FindResourceW ".echo 'FOUND: FindResourceW called'; .if (^@rdx != 0^) { du @rdx } g"
echo bp kernel32!CreateFileW ".echo 'FOUND: CreateFileW called'; .if (^@rcx != 0^) { du @rcx } g"
echo s -a 0 L?0x7fffffff "app.asar"
echo s -a 0 L?0x7fffffff "ElectronAsar"
echo s -a 0 L?0x7fffffff "Integrity"
echo .echo "Monitoring started - waiting for ASAR events..."
echo g
) > "%QUICK_SCRIPT%"

echo Quick script created: %QUICK_SCRIPT%

REM Start WinDbg
echo.
echo ================================================
echo WinDbg will start now with ASAR monitoring
echo ================================================
echo.
echo After WinDbg starts:
echo 1. Go to your VM and run: C:\Tools\claude.exe
echo 2. Watch WinDbg for "FOUND:" messages
echo 3. Press Ctrl+C in WinDbg when done
echo.
echo Starting WinDbg...

"C:\Program Files (x86)\Windows Kits\10\Debuggers\x64\windbg.exe" -k com:pipe,port=%COM_PIPE%,resets=0,reconnect -c ".scriptload \"%QUICK_SCRIPT%\""

echo.
echo Analysis session ended.
echo Check log file: %LOG_FILE%
echo.

REM Show quick results
if exist "%LOG_FILE%" (
    echo ===============================
    echo Quick Results Summary:
    echo ===============================
    
    findstr "FOUND:" "%LOG_FILE%" >nul 2>&1
    if not errorlevel 1 (
        echo ✓ ASAR events detected!
        echo.
        echo Key findings:
        findstr "FOUND:" "%LOG_FILE%"
    ) else (
        echo ⚠ No ASAR events detected
        echo This could mean:
        echo - ASAR integrity validation is disabled
        echo - Claude.exe wasn't started in VM
        echo - Breakpoints didn't hit
    )
    
    echo.
    echo Full log: %LOG_FILE%
) else (
    echo ⚠ No log file generated
)

echo.
pause
