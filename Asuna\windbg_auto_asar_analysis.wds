$$ ===================================================================
$$ Automated ASAR Integrity Analysis Script for WinDbg
$$ This script automatically sets up comprehensive ASAR monitoring
$$ Load with: .scriptload "path\to\windbg_auto_asar_analysis.wds"
$$ ===================================================================

.echo "=========================================="
.echo "  ASAR Integrity Analysis - Auto Setup"
.echo "=========================================="

$$ Enable logging with timestamp
.logopen C:\Analysis\Logs\windbg_asar_auto.log
.ttime

.echo "Setting up comprehensive ASAR analysis..."

$$ ===================================================================
$$ PHASE 1: ASAR FUNCTION BREAKPOINTS
$$ ===================================================================

.echo "Phase 1: Setting up ASAR function breakpoints..."

$$ Core ASAR integrity functions
bp claude+0x3AC730 ".echo '=== ASAR: IsEmbeddedAsarIntegrityValidationEnabled ==='; .echo 'Return value (RAX):'; r rax; .echo 'Call stack:'; k 3; .echo 'Registers:'; r; .echo ''; g"

bp claude+0x3A99C0 ".echo '=== ASAR: GetIntegrityForAsarArchive ==='; .echo 'Parameters:'; r rcx,rdx,r8,r9; .echo 'Call stack:'; k 3; .if (@rcx != 0) { .echo 'Archive path:'; du @rcx }; .if (@rdx != 0) { .echo 'Integrity data:'; db @rdx L100 }; .echo ''; g"

bp claude+0x429040 ".echo '=== ASAR: GetIntegrityFromExecutableResources ==='; .echo 'Parameters:'; r rcx,rdx; .echo 'Call stack:'; k 3; .if (@rcx != 0) { .echo 'Executable path:'; du @rcx }; .echo ''; g"

bp claude+0x337580 ".echo '=== ASAR: ValidateAsarBlockHash ==='; .echo 'Parameters:'; r rcx,rdx,r8; .echo 'Call stack:'; k 3; .if (@rcx != 0) { .echo 'Block data:'; db @rcx L64 }; .if (@rdx != 0) { .echo 'Expected hash:'; db @rdx L32 }; .echo ''; g"

bp claude+0x337790 ".echo '=== ASAR: AsarFileValidator ==='; .echo 'Parameters:'; r rcx,rdx; .echo 'Call stack:'; k 3; .if (@rcx != 0) { .echo 'File path:'; du @rcx }; .echo ''; g"

$$ Additional ASAR functions
bp claude+0x3A8B40 ".echo '=== ASAR: CreateAsarArchive ==='; r rcx,rdx; g"
bp claude+0x3A7F20 ".echo '=== ASAR: ReadAsarHeader ==='; r rcx,rdx,r8; g"
bp claude+0x3A8450 ".echo '=== ASAR: ExtractAsarFile ==='; r rcx,rdx; g"

$$ ===================================================================
$$ PHASE 2: WINDOWS API BREAKPOINTS
$$ ===================================================================

.echo "Phase 2: Setting up Windows API breakpoints..."

$$ Resource loading APIs
bp kernel32!FindResourceW ".echo '=== API: FindResourceW ==='; .echo 'Module:'; r rcx; .if (@rdx != 0) { .echo 'Type:'; du @rdx }; .if (@r8 != 0) { .echo 'Name:'; du @r8 }; .echo ''; g"

bp kernel32!FindResourceExW ".echo '=== API: FindResourceExW ==='; .echo 'Parameters:'; r rcx,rdx,r8,r9; .if (@rdx != 0) { du @rdx }; .if (@r8 != 0) { du @r8 }; g"

bp kernel32!LoadResource ".echo '=== API: LoadResource ==='; .echo 'Module/Resource:'; r rcx,rdx; .echo ''; g"

bp kernel32!LockResource ".echo '=== API: LockResource ==='; .echo 'Resource handle:'; r rcx; .echo ''; g"

bp kernel32!SizeofResource ".echo '=== API: SizeofResource ==='; .echo 'Module/Resource:'; r rcx,rdx; .echo ''; g"

$$ File operation APIs
bp kernel32!CreateFileW ".echo '=== API: CreateFileW ==='; .if (@rcx != 0) { .echo 'File path:'; du @rcx }; .echo 'Access/Share:'; r rdx,r8; .echo ''; g"

bp kernel32!ReadFile ".echo '=== API: ReadFile ==='; .echo 'Handle/Buffer/Size:'; r rcx,rdx,r8; .echo ''; g"

bp kernel32!WriteFile ".echo '=== API: WriteFile ==='; .echo 'Handle/Buffer/Size:'; r rcx,rdx,r8; .echo ''; g"

bp kernel32!GetFileSize ".echo '=== API: GetFileSize ==='; .echo 'File handle:'; r rcx; .echo ''; g"

$$ Memory mapping APIs
bp kernel32!CreateFileMappingW ".echo '=== API: CreateFileMappingW ==='; .echo 'File handle:'; r rcx; .if (@r9 != 0) { .echo 'Name:'; du @r9 }; g"

bp kernel32!MapViewOfFile ".echo '=== API: MapViewOfFile ==='; .echo 'Mapping handle:'; r rcx; .echo 'Access/Offset/Size:'; r rdx,r8,r9; g"

$$ ===================================================================
$$ PHASE 3: CRYPTOGRAPHY API BREAKPOINTS
$$ ===================================================================

.echo "Phase 3: Setting up cryptography API breakpoints..."

$$ BCrypt APIs
bp bcrypt!BCryptCreateHash ".echo '=== CRYPTO: BCryptCreateHash ==='; .echo 'Algorithm/Hash object:'; r rcx,rdx; .echo ''; g"

bp bcrypt!BCryptHashData ".echo '=== CRYPTO: BCryptHashData ==='; .echo 'Hash/Data/Size:'; r rcx,rdx,r8; .if (@rdx != 0) { .echo 'Data preview:'; db @rdx L32 }; .echo ''; g"

bp bcrypt!BCryptFinishHash ".echo '=== CRYPTO: BCryptFinishHash ==='; .echo 'Hash/Output/Size:'; r rcx,rdx,r8; .echo ''; g"

bp bcrypt!BCryptDestroyHash ".echo '=== CRYPTO: BCryptDestroyHash ==='; .echo 'Hash object:'; r rcx; .echo ''; g"

$$ CryptoAPI functions
bp crypt32!CryptHashData ".echo '=== CRYPTO: CryptHashData ==='; .echo 'Hash/Data/Size:'; r rcx,rdx,r8; g"

bp crypt32!CryptGetHashParam ".echo '=== CRYPTO: CryptGetHashParam ==='; .echo 'Hash/Param:'; r rcx,rdx; g"

$$ ===================================================================
$$ PHASE 4: MEMORY SEARCH FOR ASAR DATA
$$ ===================================================================

.echo "Phase 4: Searching for ASAR-related data in memory..."

$$ Search for ASAR strings
s -a 0 L?0x7fffffff "app.asar"
s -a 0 L?0x7fffffff "ElectronAsar"
s -a 0 L?0x7fffffff "Integrity"
s -a 0 L?0x7fffffff "sha256"
s -a 0 L?0x7fffffff "default_app.asar"

$$ Search for Unicode versions
s -u 0 L?0x7fffffff "app.asar"
s -u 0 L?0x7fffffff "ElectronAsar"
s -u 0 L?0x7fffffff "Integrity"

$$ Search for function names
s -a 0 L?0x7fffffff "IsEmbeddedAsarIntegrityValidationEnabled"
s -a 0 L?0x7fffffff "GetIntegrityForAsarArchive"
s -a 0 L?0x7fffffff "ValidateAsarBlockHash"

$$ Search for common hash patterns
s -b 0 L?0x7fffffff 7B 22 66 69 6C 65 73 22    $$ {"files"
s -b 0 L?0x7fffffff 50 4B 03 04                $$ ZIP signature
s -b 0 L?0x7fffffff 7B 22 69 6E 74 65 67 72    $$ {"integr

$$ ===================================================================
$$ PHASE 5: PROCESS AND MODULE MONITORING
$$ ===================================================================

.echo "Phase 5: Setting up process and module monitoring..."

$$ Monitor process creation
bp nt!PsCreateSystemThread ".echo '=== KERNEL: Thread created ==='; k 2; g"

$$ Monitor image loads
bp nt!MmLoadSystemImage ".echo '=== KERNEL: System image load ==='; .if (@rcx != 0) { du @rcx }; g"

$$ ===================================================================
$$ PHASE 6: ADVANCED MONITORING SETUP
$$ ===================================================================

.echo "Phase 6: Setting up advanced monitoring..."

$$ Enable special debugging features
.enable_unicode 1
.enable_long_status 1

$$ Set up automatic module load notifications
sxe ld:claude.exe ".echo '=== MODULE LOAD: claude.exe ==='; lm m claude*; .echo 'Setting deferred breakpoints...'; .reload; g"

sxe ld:kernel32.dll ".echo '=== MODULE LOAD: kernel32.dll ==='; g"

sxe ld:bcrypt.dll ".echo '=== MODULE LOAD: bcrypt.dll ==='; g"

$$ ===================================================================
$$ PHASE 7: FINAL SETUP AND START MONITORING
$$ ===================================================================

.echo "Phase 7: Final setup and start monitoring..."

$$ Display current status
.echo "Current loaded modules:"
lm

.echo "Current processes:"
!process 0 0

.echo "Breakpoint summary:"
bl

.echo ""
.echo "=========================================="
.echo "  ASAR Analysis Setup Complete!"
.echo "=========================================="
.echo ""
.echo "Monitoring is now active for:"
.echo "  - ASAR integrity validation functions"
.echo "  - Windows API calls (File/Resource/Crypto)"
.echo "  - Memory patterns and data structures"
.echo "  - Process and module loading"
.echo ""
.echo "Log file: C:\Analysis\Logs\windbg_asar_auto.log"
.echo ""
.echo "Ready to analyze! Use 'g' to continue execution."
.echo "Press Ctrl+Break to stop and examine state."
.echo ""
.echo "=========================================="

$$ Start monitoring
g
