# PowerShell Script for ASAR Integrity Resource Extraction
# Extracts PE resources containing ASAR integrity validation data

param(
    [Parameter(Mandatory=$true)]
    [string]$ExecutablePath,
    
    [Parameter(Mandatory=$false)]
    [string]$OutputDirectory = ".\extracted_resources"
)

Write-Host "=== ASAR Integrity Resource Extractor ===" -ForegroundColor Cyan
Write-Host "Target: $ExecutablePath" -ForegroundColor Yellow

# Check if file exists
if (-not (Test-Path $ExecutablePath)) {
    Write-Error "File not found: $ExecutablePath"
    exit 1
}

# Create output directory
if (-not (Test-Path $OutputDirectory)) {
    New-Item -ItemType Directory -Path $OutputDirectory | Out-Null
    Write-Host "Created output directory: $OutputDirectory" -ForegroundColor Green
}

# Add necessary .NET types for PE parsing
Add-Type -TypeDefinition @"
using System;
using System.IO;
using System.Runtime.InteropServices;
using System.Text;

public class PEResourceExtractor
{
    [DllImport("kernel32.dll", SetLastError = true)]
    public static extern IntPtr LoadLibraryEx(string lpFileName, IntPtr hReservedNull, uint dwFlags);

    [DllImport("kernel32.dll", SetLastError = true)]
    public static extern bool FreeLibrary(IntPtr hLibModule);

    [DllImport("kernel32.dll", SetLastError = true)]
    public static extern IntPtr FindResource(IntPtr hModule, string lpName, string lpType);

    [DllImport("kernel32.dll", SetLastError = true)]
    public static extern IntPtr LoadResource(IntPtr hModule, IntPtr hResInfo);

    [DllImport("kernel32.dll", SetLastError = true)]
    public static extern IntPtr LockResource(IntPtr hResData);

    [DllImport("kernel32.dll", SetLastError = true)]
    public static extern uint SizeofResource(IntPtr hModule, IntPtr hResInfo);

    [DllImport("kernel32.dll", SetLastError = true)]
    public static extern bool EnumResourceTypes(IntPtr hModule, EnumResourceTypesProc lpEnumFunc, IntPtr lParam);

    [DllImport("kernel32.dll", SetLastError = true)]
    public static extern bool EnumResourceNames(IntPtr hModule, string lpType, EnumResourceNamesProc lpEnumFunc, IntPtr lParam);

    public delegate bool EnumResourceTypesProc(IntPtr hModule, IntPtr lpType, IntPtr lParam);
    public delegate bool EnumResourceNamesProc(IntPtr hModule, IntPtr lpType, IntPtr lpName, IntPtr lParam);

    public const uint LOAD_LIBRARY_AS_DATAFILE = 0x00000002;
    public const uint LOAD_LIBRARY_AS_IMAGE_RESOURCE = 0x00000020;
}
"@

function Extract-PEResource {
    param(
        [string]$FilePath,
        [string]$ResourceType,
        [string]$ResourceName,
        [string]$OutputPath
    )
    
    try {
        Write-Host "Loading PE file as data file..." -ForegroundColor Yellow
        
        # Load the PE file as a data file
        $hModule = [PEResourceExtractor]::LoadLibraryEx($FilePath, [IntPtr]::Zero, 
            [PEResourceExtractor]::LOAD_LIBRARY_AS_DATAFILE -bor [PEResourceExtractor]::LOAD_LIBRARY_AS_IMAGE_RESOURCE)
        
        if ($hModule -eq [IntPtr]::Zero) {
            throw "Failed to load PE file: $FilePath"
        }
        
        Write-Host "Searching for resource: Type='$ResourceType', Name='$ResourceName'" -ForegroundColor Yellow
        
        # Find the resource
        $hResInfo = [PEResourceExtractor]::FindResource($hModule, $ResourceName, $ResourceType)
        
        if ($hResInfo -eq [IntPtr]::Zero) {
            Write-Warning "Resource not found: Type='$ResourceType', Name='$ResourceName'"
            return $false
        }
        
        Write-Host "Resource found! Loading resource data..." -ForegroundColor Green
        
        # Load the resource
        $hResData = [PEResourceExtractor]::LoadResource($hModule, $hResInfo)
        if ($hResData -eq [IntPtr]::Zero) {
            throw "Failed to load resource data"
        }
        
        # Lock the resource to get a pointer to the data
        $pData = [PEResourceExtractor]::LockResource($hResData)
        if ($pData -eq [IntPtr]::Zero) {
            throw "Failed to lock resource data"
        }
        
        # Get the size of the resource
        $size = [PEResourceExtractor]::SizeofResource($hModule, $hResInfo)
        if ($size -eq 0) {
            throw "Resource size is 0"
        }
        
        Write-Host "Resource size: $size bytes" -ForegroundColor Green
        
        # Copy the data to a managed byte array
        $data = New-Object byte[] $size
        [System.Runtime.InteropServices.Marshal]::Copy($pData, $data, 0, $size)
        
        # Save to file
        [System.IO.File]::WriteAllBytes($OutputPath, $data)
        Write-Host "Resource saved to: $OutputPath" -ForegroundColor Green
        
        # Try to detect data type
        $dataType = Detect-DataType -Data $data
        Write-Host "Detected data type: $dataType" -ForegroundColor Cyan
        
        # If it's text/JSON, also save as .txt for easy viewing
        if ($dataType -eq "Text/JSON") {
            $textPath = $OutputPath -replace '\.[^.]*$', '.txt'
            $text = [System.Text.Encoding]::UTF8.GetString($data)
            [System.IO.File]::WriteAllText($textPath, $text)
            Write-Host "Text version saved to: $textPath" -ForegroundColor Green
            
            # Pretty print JSON if possible
            try {
                $json = $text | ConvertFrom-Json
                $prettyJson = $json | ConvertTo-Json -Depth 10
                $prettyPath = $OutputPath -replace '\.[^.]*$', '_pretty.json'
                [System.IO.File]::WriteAllText($prettyPath, $prettyJson)
                Write-Host "Pretty JSON saved to: $prettyPath" -ForegroundColor Green
            } catch {
                Write-Host "Data is not valid JSON" -ForegroundColor Yellow
            }
        }
        
        return $true
        
    } catch {
        Write-Error "Error extracting resource: $_"
        return $false
    } finally {
        if ($hModule -ne [IntPtr]::Zero) {
            [PEResourceExtractor]::FreeLibrary($hModule) | Out-Null
        }
    }
}

function Detect-DataType {
    param([byte[]]$Data)
    
    if ($Data.Length -eq 0) {
        return "Empty"
    }
    
    # Check for text/JSON
    $text = [System.Text.Encoding]::UTF8.GetString($Data)
    if ($text -match '^[\x20-\x7E\s]*$' -and ($text.Contains('{') -or $text.Contains('['))) {
        return "Text/JSON"
    }
    
    # Check for common binary formats
    if ($Data[0] -eq 0x4D -and $Data[1] -eq 0x5A) {
        return "PE/Executable"
    }
    
    if ($Data[0] -eq 0xFF -and $Data[1] -eq 0xD8) {
        return "JPEG Image"
    }
    
    if ($Data[0] -eq 0x89 -and $Data[1] -eq 0x50 -and $Data[2] -eq 0x4E -and $Data[3] -eq 0x47) {
        return "PNG Image"
    }
    
    return "Binary"
}

function Enumerate-AllResources {
    param([string]$FilePath)
    
    Write-Host "Enumerating all resources in: $FilePath" -ForegroundColor Cyan
    
    try {
        $hModule = [PEResourceExtractor]::LoadLibraryEx($FilePath, [IntPtr]::Zero, 
            [PEResourceExtractor]::LOAD_LIBRARY_AS_DATAFILE -bor [PEResourceExtractor]::LOAD_LIBRARY_AS_IMAGE_RESOURCE)
        
        if ($hModule -eq [IntPtr]::Zero) {
            throw "Failed to load PE file for enumeration"
        }
        
        Write-Host "Successfully loaded PE file for resource enumeration" -ForegroundColor Green
        
        # Note: Resource enumeration is complex and would require additional P/Invoke setup
        # For now, we'll try the known ASAR resources
        
        return $true
        
    } catch {
        Write-Error "Error enumerating resources: $_"
        return $false
    } finally {
        if ($hModule -ne [IntPtr]::Zero) {
            [PEResourceExtractor]::FreeLibrary($hModule) | Out-Null
        }
    }
}

# Main execution
Write-Host "Starting ASAR resource extraction..." -ForegroundColor Green

# Try to extract the known ASAR integrity resource
$success = Extract-PEResource -FilePath $ExecutablePath -ResourceType "ElectronAsar" -ResourceName "Integrity" -OutputPath "$OutputDirectory\asar_integrity.bin"

if (-not $success) {
    Write-Host "Primary extraction failed. Trying alternative resource names..." -ForegroundColor Yellow
    
    # Try alternative resource identifiers
    $alternatives = @(
        @{Type="ELECTRONASAR"; Name="INTEGRITY"},
        @{Type="CUSTOM"; Name="INTEGRITY"},
        @{Type="RCDATA"; Name="INTEGRITY"},
        @{Type="10"; Name="1"},  # Numeric resource types
        @{Type="ElectronAsar"; Name="1"}
    )
    
    foreach ($alt in $alternatives) {
        Write-Host "Trying Type='$($alt.Type)', Name='$($alt.Name)'" -ForegroundColor Yellow
        $success = Extract-PEResource -FilePath $ExecutablePath -ResourceType $alt.Type -ResourceName $alt.Name -OutputPath "$OutputDirectory\asar_integrity_alt.bin"
        if ($success) {
            break
        }
    }
}

# Try to enumerate all resources
Enumerate-AllResources -FilePath $ExecutablePath

# Additional analysis
Write-Host "`n=== Additional Analysis ===" -ForegroundColor Cyan

# Check file size and basic info
$fileInfo = Get-Item $ExecutablePath
Write-Host "File size: $($fileInfo.Length) bytes" -ForegroundColor Yellow
Write-Host "Last modified: $($fileInfo.LastWriteTime)" -ForegroundColor Yellow

# Search for ASAR-related strings in the binary
Write-Host "`nSearching for ASAR-related strings..." -ForegroundColor Yellow
$content = [System.IO.File]::ReadAllBytes($ExecutablePath)
$text = [System.Text.Encoding]::ASCII.GetString($content)

$asarStrings = @("ElectronAsar", "Integrity", "app.asar", "default_app.asar", "sha256", "IsEmbeddedAsarIntegrityValidationEnabled")

foreach ($str in $asarStrings) {
    $index = $text.IndexOf($str)
    if ($index -ge 0) {
        Write-Host "Found '$str' at offset: 0x$($index.ToString('X8'))" -ForegroundColor Green
    }
}

Write-Host "`n=== Extraction Complete ===" -ForegroundColor Green
Write-Host "Check the output directory for extracted resources: $OutputDirectory" -ForegroundColor Cyan

# Summary
if ($success) {
    Write-Host "✅ Successfully extracted ASAR integrity resources!" -ForegroundColor Green
} else {
    Write-Host "❌ Failed to extract ASAR integrity resources" -ForegroundColor Red
    Write-Host "The resources might be embedded differently or use different identifiers" -ForegroundColor Yellow
}

Write-Host "`nNext steps:" -ForegroundColor Cyan
Write-Host "1. Examine extracted files in $OutputDirectory" -ForegroundColor White
Write-Host "2. Load Yui driver and run ASAR analyzer" -ForegroundColor White
Write-Host "3. Use WinDbg script for dynamic analysis" -ForegroundColor White
Write-Host "4. Compare static and dynamic analysis results" -ForegroundColor White
