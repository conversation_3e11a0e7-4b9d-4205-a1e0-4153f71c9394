@echo off
REM Build Yui Kernel Driver for ASAR Analysis

echo ===============================
echo Building Yui Kernel Driver
echo ===============================

REM Check for WDK installation
set WDK_PATH=C:\Program Files (x86)\Windows Kits\10
if not exist "%WDK_PATH%" (
    echo ERROR: Windows Driver Kit not found at %WDK_PATH%
    echo Please install WDK or use Option 1 (continue with WinDbg analysis)
    pause
    exit /b 1
)

REM Set up build environment
set BUILD_DIR=%~dp0Yui\build
if not exist "%BUILD_DIR%" mkdir "%BUILD_DIR%"

echo Setting up WDK build environment...
call "%WDK_PATH%\bin\SetupVSEnv.cmd"

echo Building Yui driver...
cd /d "%~dp0Yui"

REM Simple driver build command
cl /kernel /c src\main.cpp /Fo:build\main.obj
if errorlevel 1 (
    echo ERROR: Failed to compile driver source
    pause
    exit /b 1
)

link /driver /entry:DriverEntry build\main.obj /out:build\Yui.sys
if errorlevel 1 (
    echo ERROR: Failed to link driver
    pause
    exit /b 1
)

echo.
echo ===============================
echo Yui Driver Build Complete!
echo ===============================
echo Driver location: %BUILD_DIR%\Yui.sys
echo.
echo Next steps:
echo 1. Copy Yui.sys to VM: copy "%BUILD_DIR%\Yui.sys" "\\KernelDevelopment\C$\Tools\"
echo 2. Load driver in VM: sc create Yui binPath="C:\Tools\Yui.sys" type=kernel
echo 3. Start driver: sc start Yui
echo 4. Run ASAR analyzer again
echo.
pause
