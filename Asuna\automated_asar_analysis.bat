@echo off
REM Automated ASAR Integrity Analysis - Batch Script Version
REM This script automates the ASAR analysis workflow using command line tools

setlocal enabledelayedexpansion

echo =================================
echo Automated ASAR Integrity Analysis
echo =================================

REM Configuration
set VM_NAME=KernelDevelopment
set COM_PIPE=\\.\pipe\com_1
set LOG_DIR=C:\Analysis\Logs
set TOOLS_DIR=C:\Users\<USER>\AppData\Local\AnthropicClaude\Asuna
set TIMEOUT_MINUTES=10

REM Create timestamp for logs
for /f "tokens=2 delims==" %%a in ('wmic OS Get localdatetime /value') do set "dt=%%a"
set "timestamp=%dt:~0,4%%dt:~4,2%%dt:~6,2%_%dt:~8,2%%dt:~10,2%%dt:~12,2%"

REM Create log directory
if not exist "%LOG_DIR%" mkdir "%LOG_DIR%"

set LOG_FILE=%LOG_DIR%\asar_analysis_%timestamp%.log
set WINDBG_SCRIPT=%LOG_DIR%\windbg_commands_%timestamp%.txt
set REPORT_FILE=%LOG_DIR%\asar_report_%timestamp%.txt

echo [%time%] Starting automated ASAR analysis >> "%LOG_FILE%"
echo [%time%] Log file: %LOG_FILE% >> "%LOG_FILE%"

echo.
echo Step 1: Checking VM status...
echo [%time%] Checking VM status >> "%LOG_FILE%"

REM Check if VM is running
tasklist | findstr vmwp >nul
if errorlevel 1 (
    echo VM not running - please start KernelDevelopment VM manually
    echo [%time%] ERROR: VM not running >> "%LOG_FILE%"
    pause
    exit /b 1
) else (
    echo VM is running
    echo [%time%] VM is running >> "%LOG_FILE%"
)

echo.
echo Step 2: Creating WinDbg automation script...
echo [%time%] Creating WinDbg script >> "%LOG_FILE%"

REM Create WinDbg automation script
(
echo .echo "=== Automated ASAR Analysis Started ==="
echo .logopen "%LOG_FILE%"
echo .ttime
echo.
echo .echo "=== Setting up ASAR breakpoints ==="
echo bp claude+0x3AC730 ".echo 'ASAR: IsEmbeddedAsarIntegrityValidationEnabled'; r rax; k 3; g"
echo bp claude+0x3A99C0 ".echo 'ASAR: GetIntegrityForAsarArchive'; r rcx,rdx,r8; k 3; g"
echo bp claude+0x429040 ".echo 'ASAR: GetIntegrityFromExecutableResources'; r rcx,rdx; k 3; g"
echo bp claude+0x337580 ".echo 'ASAR: ValidateAsarBlockHash'; r rcx,rdx,r8; k 3; g"
echo bp claude+0x337790 ".echo 'ASAR: AsarFileValidator'; r rcx,rdx; k 3; g"
echo.
echo .echo "=== Setting up API breakpoints ==="
echo bp kernel32!FindResourceW ".echo 'API: FindResourceW'; .if (^@rdx != 0^) { du @rdx } .if (^@r8 != 0^) { du @r8 } g"
echo bp kernel32!LoadResource ".echo 'API: LoadResource'; r rcx,rdx; g"
echo bp kernel32!CreateFileW ".echo 'API: CreateFileW'; .if (^@rcx != 0^) { du @rcx } g"
echo.
echo .echo "=== Setting up crypto breakpoints ==="
echo bp bcrypt!BCryptCreateHash ".echo 'CRYPTO: BCryptCreateHash'; r rcx,rdx; g"
echo bp bcrypt!BCryptHashData ".echo 'CRYPTO: BCryptHashData'; r rcx,rdx,r8; g"
echo bp bcrypt!BCryptFinishHash ".echo 'CRYPTO: BCryptFinishHash'; r rcx,rdx,r8; g"
echo.
echo .echo "=== Searching for ASAR strings ==="
echo s -a 0 L?0x7fffffff "app.asar"
echo s -a 0 L?0x7fffffff "ElectronAsar"
echo s -a 0 L?0x7fffffff "Integrity"
echo s -a 0 L?0x7fffffff "sha256"
echo.
echo .echo "=== Starting execution monitoring ==="
echo g
) > "%WINDBG_SCRIPT%"

echo WinDbg script created: %WINDBG_SCRIPT%
echo [%time%] WinDbg script created >> "%LOG_FILE%"

echo.
echo Step 3: Starting WinDbg with automation...
echo [%time%] Starting WinDbg >> "%LOG_FILE%"

REM Start WinDbg in background
start "WinDbg ASAR Analysis" windbg -k com:pipe,port=%COM_PIPE%,resets=0,reconnect -c ".scriptload \"%WINDBG_SCRIPT%\""

echo WinDbg started - waiting for initialization...
echo [%time%] WinDbg started >> "%LOG_FILE%"
timeout /t 10 /nobreak >nul

echo.
echo Step 4: Analysis is now running...
echo [%time%] Analysis running >> "%LOG_FILE%"

echo.
echo ============================================
echo ASAR Analysis is now running automatically!
echo ============================================
echo.
echo What's happening:
echo - WinDbg is monitoring Claude.exe for ASAR function calls
echo - Breakpoints are set on key integrity validation functions
echo - All events are being logged to: %LOG_FILE%
echo.
echo Manual steps to complete in VM:
echo 1. Start Claude.exe in VM: C:\Tools\claude.exe
echo 2. Start ASAR analyzer in VM: C:\Tools\asar_analyzer.exe
echo.
echo The analysis will run for %TIMEOUT_MINUTES% minutes
echo Watch the WinDbg window for breakpoint hits!
echo.

REM Wait and monitor
echo [%time%] Monitoring analysis for %TIMEOUT_MINUTES% minutes >> "%LOG_FILE%"
echo Monitoring analysis progress...

REM Simple monitoring loop
set /a TIMEOUT_SECONDS=%TIMEOUT_MINUTES%*60
set /a ELAPSED=0

:monitor_loop
if %ELAPSED% geq %TIMEOUT_SECONDS% goto analysis_complete

REM Check if log file has new ASAR events
if exist "%LOG_FILE%" (
    findstr /C:"ASAR:" /C:"CRYPTO:" /C:"API:" "%LOG_FILE%" >nul 2>&1
    if not errorlevel 1 (
        echo [%time%] ASAR events detected in log
    )
)

timeout /t 30 /nobreak >nul
set /a ELAPSED+=30
goto monitor_loop

:analysis_complete
echo.
echo Step 5: Generating analysis report...
echo [%time%] Generating report >> "%LOG_FILE%"

REM Generate simple report
(
echo =================================
echo ASAR Integrity Analysis Report
echo =================================
echo Generated: %date% %time%
echo Log File: %LOG_FILE%
echo.
echo === Summary ===
) > "%REPORT_FILE%"

if exist "%LOG_FILE%" (
    echo Analyzing log file... >> "%REPORT_FILE%"
    
    REM Count events
    for /f %%i in ('findstr /C:"ASAR:" "%LOG_FILE%" ^| find /c /v ""') do set ASAR_COUNT=%%i
    for /f %%i in ('findstr /C:"CRYPTO:" "%LOG_FILE%" ^| find /c /v ""') do set CRYPTO_COUNT=%%i
    for /f %%i in ('findstr /C:"API:" "%LOG_FILE%" ^| find /c /v ""') do set API_COUNT=%%i
    
    echo ASAR Function Calls: !ASAR_COUNT! >> "%REPORT_FILE%"
    echo Crypto Operations: !CRYPTO_COUNT! >> "%REPORT_FILE%"
    echo API Calls: !API_COUNT! >> "%REPORT_FILE%"
    echo. >> "%REPORT_FILE%"
    echo === Key Events === >> "%REPORT_FILE%"
    
    REM Add interesting events
    findstr /C:"ASAR:" /C:"CRYPTO:" /C:"API:" "%LOG_FILE%" >> "%REPORT_FILE%"
) else (
    echo No log file found >> "%REPORT_FILE%"
)

echo.
echo ========================================
echo Automated ASAR Analysis Complete!
echo ========================================
echo.
echo Results:
echo   Log File: %LOG_FILE%
echo   Report:   %REPORT_FILE%
echo   WinDbg Script: %WINDBG_SCRIPT%
echo.
echo [%time%] Analysis completed >> "%LOG_FILE%"

echo Check the files above for detailed analysis results.
echo.
pause
