;
; Yui.inf
;

[Version]
Signature   = "$WINDOWS NT$"
Class       = System ; TODO: specify appropriate Class
ClassGuid   = {4d36e97d-e325-11ce-bfc1-08002be10318} ; TODO: specify appropriate ClassGuid
Provider    = %ManufacturerName%
CatalogFile = Yui.cat
DriverVer = 06/02/2025,0.2.48.321
PnpLockdown = 1

[DestinationDirs]
DefaultDestDir = 13

[SourceDisksNames]
1 = %DiskName%,,,""

[SourceDisksFiles]
Yui.sys = 1,,

;*****************************************
; Install Section
;*****************************************

[Manufacturer]
%ManufacturerName% = Standard,NTamd64.10.0...16299 ; %13% support introduced in build 16299

[Standard.NTamd64.10.0...16299]
%Yui.DeviceDesc% = Yui_Device, Root\Yui ; TODO: edit hw-id

[Yui_Device.NT]
CopyFiles = File_Copy

[File_Copy]
Yui.sys

;-------------- Service installation
[Yui_Device.NT.Services]
AddService = Yui,%SPSVCINST_ASSOCSERVICE%, Yui_Service_Inst

; -------------- Yui driver install sections
[Yui_Service_Inst]
DisplayName    = %Yui.SVCDESC%
ServiceType    = 1               ; SERVICE_KERNEL_DRIVER
StartType      = 3               ; SERVICE_DEMAND_START
ErrorControl   = 1               ; SERVICE_ERROR_NORMAL
ServiceBinary  = %13%\Yui.sys

[Yui_Device.NT.Wdf]
KmdfService = Yui, Yui_wdfsect

[Yui_wdfsect]
KmdfLibraryVersion = 1.15

[Strings]
SPSVCINST_ASSOCSERVICE = 0x00000002
ManufacturerName = "<Your manufacturer name>" ;TODO: Replace with your manufacturer name
DiskName = "Yui Installation Disk"
Yui.DeviceDesc = "Yui Device"
Yui.SVCDESC = "Yui Service"
