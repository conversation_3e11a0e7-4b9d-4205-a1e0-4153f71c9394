@echo off
REM Build Yui Kernel Driver using Windows Driver Kit (WDK)

echo ===============================
echo Building Yui Kernel Driver with WDK
echo ===============================

REM Set WDK paths
set WDK_PATH=C:\Program Files (x86)\Windows Kits\10
set WDK_BIN=%WDK_PATH%\bin\10.0.26100.0\x64
set WDK_INC=%WDK_PATH%\Include\10.0.26100.0
set WDK_LIB=%WDK_PATH%\Lib\10.0.26100.0

REM Check WDK installation
if not exist "%WDK_PATH%" (
    echo ERROR: WDK not found at %WDK_PATH%
    pause
    exit /b 1
)

echo Found WDK at: %WDK_PATH%

REM Create build directory
set BUILD_DIR=%~dp0Yui\build
if not exist "%BUILD_DIR%" mkdir "%BUILD_DIR%"

echo Setting up WDK build environment...

REM Set up environment variables for kernel driver build
set INCLUDE=%WDK_INC%\km;%WDK_INC%\shared;%WDK_INC%\km\crt
set LIB=%WDK_LIB%\km\x64
set PATH=%WDK_BIN%;%PATH%

echo Building Yui driver...
cd /d "%~dp0Yui"

REM Compile the driver
echo Compiling driver source...
cl.exe /c /kernel /W3 /Gy /Gm- /Zp8 /GS- /fp:precise /Zc:wchar_t- /Zc:forScope /GR- /Gz /hotpatch /EHs-c- /D_WIN64 /D_AMD64_ /DAMD64 /D_KERNEL_MODE /D_WIN32_WINNT=0x0601 /DWINVER=0x0601 /DWINNT=1 /D_WIN32_IE=0x0800 /DDEVL=1 /DNDEBUG /DKMDF_VERSION_MAJOR=1 /DKMDF_VERSION_MINOR=15 /I"%WDK_INC%\km" /I"%WDK_INC%\shared" /I"%WDK_INC%\km\crt" src\main.cpp /Fo:build\main.obj

if errorlevel 1 (
    echo ERROR: Failed to compile driver source
    pause
    exit /b 1
)

echo Linking driver...
link.exe /DRIVER /ENTRY:DriverEntry /SUBSYSTEM:NATIVE /MACHINE:X64 /LIBPATH:"%WDK_LIB%\km\x64" ntoskrnl.lib hal.lib build\main.obj /OUT:build\Yui.sys

if errorlevel 1 (
    echo ERROR: Failed to link driver
    pause
    exit /b 1
)

echo.
echo ===============================
echo SUCCESS: Yui Driver Built!
echo ===============================
echo Driver location: %BUILD_DIR%\Yui.sys
echo Driver size:
dir "%BUILD_DIR%\Yui.sys"

echo.
echo Next steps to load driver in VM:
echo 1. Copy driver to VM
echo 2. Load and start driver
echo 3. Run ASAR analyzer
echo.

REM Create deployment script
echo Creating deployment script...
(
echo @echo off
echo REM Deploy Yui Driver to KernelDevelopment VM
echo echo Copying Yui driver to VM...
echo copy "%BUILD_DIR%\Yui.sys" "\\KernelDevelopment\C$\Tools\"
echo echo.
echo echo In VM, run these commands as Administrator:
echo echo sc create Yui binPath=C:\Tools\Yui.sys type=kernel start=demand
echo echo sc start Yui
echo echo.
echo echo Then run ASAR analyzer: C:\Tools\asar_analyzer.exe
echo pause
) > deploy_yui.bat

echo Deployment script created: deploy_yui.bat
echo.
pause
