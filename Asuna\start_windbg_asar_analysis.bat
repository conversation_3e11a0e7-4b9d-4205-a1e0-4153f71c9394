@echo off
REM Automated WinDbg ASAR Analysis Launcher
REM This script starts WinDbg with automatic ASAR analysis setup

echo =======================================
echo  WinDbg ASAR Analysis Auto-Launcher
echo =======================================

set SCRIPT_PATH=%~dp0windbg_auto_asar_analysis.wds
set LOG_DIR=C:\Analysis\Logs
set COM_PIPE=\\.\pipe\com_1

REM Create log directory
if not exist "%LOG_DIR%" mkdir "%LOG_DIR%"

echo Script path: %SCRIPT_PATH%
echo Log directory: %LOG_DIR%
echo COM pipe: %COM_PIPE%
echo.

REM Check if script exists
if not exist "%SCRIPT_PATH%" (
    echo ERROR: WinDbg script not found at %SCRIPT_PATH%
    pause
    exit /b 1
)

echo Starting WinDbg with automatic ASAR analysis...
echo.
echo What will happen:
echo 1. WinDbg connects to KernelDevelopment VM via COM pipe
echo 2. Automatic script loads and sets up all breakpoints
echo 3. ASAR monitoring begins immediately
echo 4. All events logged to %LOG_DIR%\windbg_asar_auto.log
echo.
echo After WinDbg starts:
echo - The script will run automatically
echo - All ASAR breakpoints will be set
echo - Monitoring will begin
echo - Use 'g' to continue if needed
echo.

REM Start WinDbg with admin privileges and auto-load script
powershell -Command "Start-Process '%LocalAppData%\Microsoft\WindowsApps\WinDbgX.exe' -ArgumentList '-k com:pipe,port=%COM_PIPE%,resets=0,reconnect', '-c \".scriptload \`\"%SCRIPT_PATH%\`\"\"' -Verb RunAs"

echo.
echo WinDbg launched with automatic ASAR analysis!
echo Check the WinDbg window for setup progress.
echo.
pause
