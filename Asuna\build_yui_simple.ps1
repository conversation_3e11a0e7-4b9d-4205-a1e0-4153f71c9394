# Simple Yui Driver Build Script
# This script builds the Yui kernel driver for ASAR analysis

Write-Host "===============================" -ForegroundColor Cyan
Write-Host "Building Yui Kernel Driver" -ForegroundColor Cyan
Write-Host "===============================" -ForegroundColor Cyan

# Check if we have Visual Studio build tools
$vsPath = "C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvarsall.bat"
if (-not (Test-Path $vsPath)) {
    $vsPath = "C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\VC\Auxiliary\Build\vcvarsall.bat"
}

if (-not (Test-Path $vsPath)) {
    Write-Host "ERROR: Visual Studio build tools not found" -ForegroundColor Red
    Write-Host "Please install Visual Studio or continue with WinDbg analysis only" -ForegroundColor Yellow
    Read-Host "Press Enter to continue"
    exit 1
}

# Create build directory
$buildDir = ".\Yui\build"
if (-not (Test-Path $buildDir)) {
    New-Item -ItemType Directory -Path $buildDir -Force | Out-Null
}

Write-Host "Setting up build environment..." -ForegroundColor Yellow

# Simple approach: Create a minimal driver using existing tools
$driverSource = @"
#include <ntifs.h>

extern "C" {
    NTKERNELAPI NTSTATUS IoCreateDriver(PUNICODE_STRING DriverName, PDRIVER_INITIALIZE InitializationFunction);
}

NTSTATUS DriverEntry() {
    DbgPrint("[+] Yui driver loaded for ASAR analysis\n");
    return STATUS_SUCCESS;
}
"@

# Write minimal driver source
$minimalSource = ".\Yui\build\minimal_yui.cpp"
Set-Content -Path $minimalSource -Value $driverSource

Write-Host "Building minimal Yui driver..." -ForegroundColor Yellow

# Try to build using Visual Studio tools
try {
    # Set up environment and build
    $buildCmd = @"
call "$vsPath" x64
cl /kernel /c "$minimalSource" /Fo:".\Yui\build\yui.obj"
link /driver /entry:DriverEntry ".\Yui\build\yui.obj" /out:".\Yui\build\Yui.sys"
"@
    
    $buildScript = ".\Yui\build\build.bat"
    Set-Content -Path $buildScript -Value $buildCmd
    
    & cmd /c $buildScript
    
    if (Test-Path ".\Yui\build\Yui.sys") {
        Write-Host "SUCCESS: Yui driver built!" -ForegroundColor Green
        Write-Host "Driver location: $(Resolve-Path '.\Yui\build\Yui.sys')" -ForegroundColor White
        
        Write-Host "`nNext steps:" -ForegroundColor Cyan
        Write-Host "1. Copy to VM: copy '.\Yui\build\Yui.sys' to VM C:\Tools\" -ForegroundColor White
        Write-Host "2. In VM: sc create Yui binPath=C:\Tools\Yui.sys type=kernel" -ForegroundColor White
        Write-Host "3. In VM: sc start Yui" -ForegroundColor White
        Write-Host "4. Run ASAR analyzer again" -ForegroundColor White
    } else {
        throw "Driver build failed"
    }
} catch {
    Write-Host "ERROR: Failed to build driver" -ForegroundColor Red
    Write-Host "Error: $_" -ForegroundColor Red
    Write-Host "`nRECOMMENDATION: Continue with WinDbg analysis" -ForegroundColor Yellow
    Write-Host "The kernel debugging is already providing excellent ASAR analysis!" -ForegroundColor Green
}

Write-Host "`nPress Enter to continue..." -ForegroundColor Gray
Read-Host
