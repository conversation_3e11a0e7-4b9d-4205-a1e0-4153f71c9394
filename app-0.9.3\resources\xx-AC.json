{"+/cwsayrqk": [{"type": 0, "value": "ACTUAL SIZE"}], "+7sd9hoyZA": [{"type": 0, "value": "COPY"}], "/PgA81GVOD": [{"type": 0, "value": "EDIT"}], "/bRGKhnXQ6": [{"type": 0, "value": "A NEW VERSION IS AVAILABLE. IT WILL BE DOWNLOADED AND INSTALLED AUTOMATICALLY."}], "0g8/VVdNuN": [{"type": 0, "value": "GO TO CLAUDE.AI/SETTINGS TO CONFIGURE YOUR PROFILE, TEAM, AND MORE."}], "0tZLEYF8mJ": [{"type": 0, "value": "DEVELOPER"}], "1HUTYwndT2": [{"type": 0, "value": "WINDOW"}], "1PfZLi/OV7": [{"type": 0, "value": "GENERAL"}], "1TJUzU26sO": [{"type": 0, "value": "SUBMIT FEEDBACK…"}], "25aCMlTDUq": [{"type": 0, "value": "AUTOMATICALLY START CLAUDE WHEN YOU LOG IN"}], "3ML3xT+gEV": [{"type": 0, "value": "REDO"}], "3gG1j3kRBX": [{"type": 0, "value": "SUBMIT FEEDBACK..."}], "3unrKzH4zB": [{"type": 0, "value": "COPY"}], "4qP7MjrQfC": [{"type": 0, "value": "ENVIRONMENT VARIABLES"}], "5DUIVR3fVi": [{"type": 0, "value": "ABOUT..."}], "6yv8ytK4El": [{"type": 0, "value": "CHECK YOUR NETWORK CONNECTION"}], "7fdcqxofEs": [{"type": 0, "value": "EXIT"}], "7gSC+rZzXX": [{"type": 0, "value": "QUI<PERSON><PERSON><PERSON><PERSON> OPEN CLAUDE FROM ANYWHERE"}], "8YQEOfuaGO": [{"type": 0, "value": "SELECT ALL"}], "9+afSO9e/t": [{"type": 0, "value": "FAILED TO CHECK FOR UPDATES: "}, {"type": 1, "value": "error"}], "9uNxNtcrFI": [{"type": 0, "value": "INSTALL"}], "CZwl8X2D85": [{"type": 0, "value": "ADVANCED OPTIONS"}], "CizRPROPWo": [{"type": 0, "value": "CLAUDE HELP"}], "D43DeqP+2t": [{"type": 0, "value": "CLAUDE SETTINGS"}], "D4DyT6MmPy": [{"type": 0, "value": "COULD NOT LOAD APP SETTINGS"}], "DQTgg21B7g": [{"type": 0, "value": "SHOW APP"}], "E9jYTa7AbX": [{"type": 0, "value": "SYSTEM TRAY"}], "EfdnINFnIz": [{"type": 0, "value": "FILE"}], "GSG5S0ysrR": [{"type": 0, "value": "RUN ON STARTUP"}], "HeHYq6bbS2": [{"type": 0, "value": "CLAUDE CAN RECEIVE INFORMATION LIKE PROMPTS AND ATTACHMENTS FROM SPECIALIZED SERVERS USING MODEL CONTEXT PROTOCOL."}], "I5O68ogAtr": [{"type": 0, "value": "GET STARTED"}], "JVwNvMZjVT": [{"type": 0, "value": "PASTE"}], "KAo3lt5Hv+": [{"type": 0, "value": "PASTE"}], "Ko/2Ml7mZG": [{"type": 0, "value": "RELOAD THIS PAGE"}], "L32WRR6NOL": [{"type": 0, "value": "DELETE"}], "L717supPIA": [{"type": 0, "value": "SETTINGS"}], "LCWUQ/4Fu6": [{"type": 0, "value": "VIEW"}], "NZIwKxgxJ+": [{"type": 0, "value": "ARE YOU SURE YOU WANT TO REMOVE THE MCP SERVER \""}, {"type": 1, "value": "server<PERSON>ey"}, {"type": 0, "value": "\"?"}], "Nmvo1ufAY5": [{"type": 0, "value": "COULDN'T CONNECT TO CLAUDE"}], "O3rtEd7aMd": [{"type": 0, "value": "FIND"}], "ODySlGptaj": [{"type": 0, "value": "SETTINGS…"}], "PH29MShDiy": [{"type": 0, "value": "FORWARD"}], "PW5U8NgTto": [{"type": 0, "value": "OPEN MCP LOG FILE..."}], "PZtcoAOSsa": [{"type": 0, "value": "DON'T ENABLE"}], "PbJ4jR0kv1": [{"type": 0, "value": "YOU ARE RUNNING THE LATEST VERSION."}], "RTg057HE1D": [{"type": 0, "value": "SHOW DEV TOOLS"}], "S3MXlbjkax": [{"type": 0, "value": "WHAT CAN I HELP YOU WITH TODAY?"}], "S3k5yXss2r": [{"type": 0, "value": "VERSION "}, {"type": 1, "value": "version"}], "TH+W2Ad73P": [{"type": 0, "value": "CUT"}], "UJCjEVPX6Q": [{"type": 0, "value": "LOOK UP"}], "Vvus2ifAny": [{"type": 0, "value": "EDIT CONFIG"}], "W1pELwt/+a": [{"type": 0, "value": "CLAUDE RUNS IN THE NOTIFICATION AREA"}], "WBvq3HlPae": [{"type": 0, "value": "SET SHORTCUT"}], "WF1HSu0jAC": [{"type": 0, "value": "OPEN LOGS FOLDER"}], "WZe86KSdrM": [{"type": 0, "value": "ADD TO DICTIONARY"}], "WlhIx7DfFO": [{"type": 0, "value": "OK"}], "XPIoFTkh3e": [{"type": 0, "value": "NO UPDATE AVAILABLE"}], "XZ36+EBE5/": [{"type": 0, "value": "ZOOM OUT"}], "XinCguXCgN": [{"type": 0, "value": "LEARN MORE"}], "YTdYCYAf/Z": [{"type": 0, "value": "SHOW CLAUDE IN THE MENU BAR"}], "Z9g5m/V9Nq": [{"type": 0, "value": "ZOOM IN"}], "ZJZN1+KyJw": [{"type": 0, "value": "SETTINGS..."}], "aNmxuDcWaU": [{"type": 0, "value": "ERROR"}], "aXdFLiVzjd": [{"type": 0, "value": "SHOW MAIN WINDOW"}], "arbRxbtBkP": [{"type": 0, "value": "BACK"}], "baGq3gy8z1": [{"type": 0, "value": "NEW CONVERSATION"}], "dKX0bpR+a2": [{"type": 0, "value": "QUIT"}], "dLyz0Srosd": [{"type": 0, "value": "DEVELOPER"}], "fEeEFfSz4K": [{"type": 0, "value": "ZOOM IN (INDIE COOLER VERSION)"}], "fFJxOwJRj2": [{"type": 0, "value": "UNDO"}], "fWDSQQgRO5": [{"type": 0, "value": "MENU BAR"}], "iFRmqBsr1N": [{"type": 0, "value": "ABOUT CLAUDE"}], "ilE9e0uxNN": [{"type": 0, "value": "REFRESH"}], "j66cdL4EK5": [{"type": 0, "value": "OPEN DOCUMENTATION"}], "jd5ZNrRMNP": [{"type": 0, "value": "YOU'RE AN EARLY EXPLORER, SO LET US KNOW YOUR FEEDBACK."}], "k+06oXbIas": [{"type": 0, "value": "SHOW CLAUDE IN THE NOTIFICATIONS AREA"}], "kYwW0OsI4M": [{"type": 0, "value": "CLAUDE DESKTOP IS IN BETA."}], "m3GfpKD1WX": [{"type": 0, "value": "RELOAD"}], "mRXjxhS6p4": [{"type": 0, "value": "CHECK FOR UPDATES…"}], "ngLpGT7bUJ": [{"type": 0, "value": "COULD NOT LOAD DEVELOPER SETTINGS"}], "oQuOiX24pp": [{"type": 0, "value": "QUIT"}], "pWXxZASpOB": [{"type": 0, "value": "HELP"}], "pgaCSv2/6H": [{"type": 0, "value": "ARGUMENTS"}], "q4hs14B00V": [{"type": 0, "value": "UNKNOWN ERROR"}], "rNAd+HxSK4": [{"type": 0, "value": "OPEN MCP LOG FILE"}], "rY99UXvTDU": [{"type": 0, "value": "COPY IMAGE"}], "rdiPpQVqvY": [{"type": 0, "value": "DEVELOPER MODE ALLOWS ACCESS TO DEVELOPER TOOLS AND DEBUGGING FEATURES. ONLY ENABLE THIS IF YOU KNOW WHAT YOU'RE DOING."}], "rwFEudHXey": [{"type": 0, "value": "THERE WAS AN ERROR READING OR PARSING CLAUDE_DESKTOP_CONFIG.JSON: "}, {"type": 1, "value": "error"}], "sNnRQsIEYz": [{"type": 0, "value": "FIND IN PAGE"}], "sZxWXq9BzJ": [{"type": 0, "value": "GIVE FEEDBACK"}], "sys7RHphmL": [{"type": 0, "value": "FEEDBACK"}], "tWutslc/9Z": [{"type": 0, "value": "ENABLE"}], "u1/hT7oRQY": [{"type": 0, "value": "QUICK ENTRY KEYBOARD SHORTCUT"}], "uc3dnSo+eo": [{"type": 0, "value": "FILE"}], "urCd4k/cE0": [{"type": 0, "value": "COMMAND"}], "vgLHPxjh9O": [{"type": 0, "value": "ENABLE DEVELOPER MODE"}], "wS64bVG2CO": [{"type": 0, "value": "MCP IS A PROTOCOL THAT ENABLES SECURE CONNECTIONS BETWEEN CLIENTS, SUCH AS THE CLAUDE DESKTOP APP, AND LOCAL SERVICES."}], "xJs1jZ8PoA": [{"type": 0, "value": "<PERSON><PERSON><PERSON><PERSON> RUNS IN THE BACKGROUND EVEN WHEN YOU CLOSE THE WINDOW. CLICK THE CLAUDE ICON IN THE TRAY TO REOPEN THE APP, OR RIGHT-CLICK TO QUIT."}], "xKRKzVVy9c": [{"type": 0, "value": "CONFIGURE"}], "xd436TVDRZ": [{"type": 0, "value": "THERE WAS AN ERROR READING OR PARSING DEVELOPER_SETTINGS.JSON: "}, {"type": 1, "value": "error"}], "y9tCbmRzHN": [{"type": 0, "value": "ENABLE DEVELOPER MODE?"}], "ytjMRobdyL": [{"type": 0, "value": "UPDATE AVAILABLE"}], "zAYm/Z684h": [{"type": 0, "value": "HELP"}], "zCIK9K8J4a": [{"type": 0, "value": "ERROR"}], "zSP70MVzIo": [{"type": 0, "value": "CLEAR SHORTCUT"}]}