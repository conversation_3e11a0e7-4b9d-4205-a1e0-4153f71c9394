﻿[01/06/25 09:38:58] info: Program: Starting Squirrel Updater: --checkForUpdate https://storage.googleapis.com/osprey-downloads-c02f6a0d-347c-492b-a752-3e0651722e97/nest-win-x64
[01/06/25 09:38:58] info: Program: Fetching update information, downloading from https://storage.googleapis.com/osprey-downloads-c02f6a0d-347c-492b-a752-3e0651722e97/nest-win-x64
[01/06/25 09:38:58] warn: SingleGlobalInstance: Failed to grab lockfile, will retry: C:\Users\<USER>\AppData\Local\Temp\.squirrel-lock-25193F8A1C4880F0B680FE7134E0F6A3B8A279AF: System.IO.IOException: The process cannot access the file 'C:\Users\<USER>\AppData\Local\Temp\.squirrel-lock-25193F8A1C4880F0B680FE7134E0F6A3B8A279AF' because it is being used by another process.
   at System.IO.__Error.WinIOError(Int32 errorCode, String maybeFullPath)
   at System.IO.FileStream.Init(String path, FileMode mode, FileAccess access, Int32 rights, Boolean useRights, FileShare share, Int32 bufferSize, FileOptions options, SECURITY_ATTRIBUTES secAttrs, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.FileStream..ctor(String path, FileMode mode, FileAccess access, FileShare share)
   at Squirrel.SingleGlobalInstance..ctor(String key, TimeSpan timeOut)
[01/06/25 09:38:59] info: Program: Starting Squirrel Updater: --checkForUpdate https://storage.googleapis.com/osprey-downloads-c02f6a0d-347c-492b-a752-3e0651722e97/nest-win-x64
[01/06/25 09:38:59] info: Program: Fetching update information, downloading from https://storage.googleapis.com/osprey-downloads-c02f6a0d-347c-492b-a752-3e0651722e97/nest-win-x64
[01/06/25 09:38:59] warn: SingleGlobalInstance: Failed to grab lockfile, will retry: C:\Users\<USER>\AppData\Local\Temp\.squirrel-lock-25193F8A1C4880F0B680FE7134E0F6A3B8A279AF: System.IO.IOException: The process cannot access the file 'C:\Users\<USER>\AppData\Local\Temp\.squirrel-lock-25193F8A1C4880F0B680FE7134E0F6A3B8A279AF' because it is being used by another process.
   at System.IO.__Error.WinIOError(Int32 errorCode, String maybeFullPath)
   at System.IO.FileStream.Init(String path, FileMode mode, FileAccess access, Int32 rights, Boolean useRights, FileShare share, Int32 bufferSize, FileOptions options, SECURITY_ATTRIBUTES secAttrs, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.FileStream..ctor(String path, FileMode mode, FileAccess access, FileShare share)
   at Squirrel.SingleGlobalInstance..ctor(String key, TimeSpan timeOut)
[01/06/25 09:38:59] warn: SingleGlobalInstance: Failed to grab lockfile, will retry: C:\Users\<USER>\AppData\Local\Temp\.squirrel-lock-25193F8A1C4880F0B680FE7134E0F6A3B8A279AF: System.IO.IOException: The process cannot access the file 'C:\Users\<USER>\AppData\Local\Temp\.squirrel-lock-25193F8A1C4880F0B680FE7134E0F6A3B8A279AF' because it is being used by another process.
   at System.IO.__Error.WinIOError(Int32 errorCode, String maybeFullPath)
   at System.IO.FileStream.Init(String path, FileMode mode, FileAccess access, Int32 rights, Boolean useRights, FileShare share, Int32 bufferSize, FileOptions options, SECURITY_ATTRIBUTES secAttrs, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.FileStream..ctor(String path, FileMode mode, FileAccess access, FileShare share)
   at Squirrel.SingleGlobalInstance..ctor(String key, TimeSpan timeOut)
[01/06/25 09:38:59] info: CheckForUpdateImpl: Generated new staging user ID: 9109ca86-fb15-5c28-8ff2-4c2cf134764d
[01/06/25 09:39:00] info: CheckForUpdateImpl: Downloading RELEASES file from https://storage.googleapis.com/osprey-downloads-c02f6a0d-347c-492b-a752-3e0651722e97/nest-win-x64
[01/06/25 09:39:00] info: FileDownloader: Downloading url: https://storage.googleapis.com/osprey-downloads-c02f6a0d-347c-492b-a752-3e0651722e97/nest-win-x64/RELEASES?id=AnthropicClaude&localVersion=0.9.3&arch=amd64
[01/06/25 09:39:00] info: Program: Finished Squirrel Updater
[01/06/25 09:39:18] info: Program: Starting Squirrel Updater: --checkForUpdate https://storage.googleapis.com/osprey-downloads-c02f6a0d-347c-492b-a752-3e0651722e97/nest-win-x64
[01/06/25 09:39:18] info: Program: Fetching update information, downloading from https://storage.googleapis.com/osprey-downloads-c02f6a0d-347c-492b-a752-3e0651722e97/nest-win-x64
[01/06/25 09:39:18] info: CheckForUpdateImpl: Using existing staging user ID: 9109ca86-fb15-5c28-8ff2-4c2cf134764d
[01/06/25 09:39:18] info: CheckForUpdateImpl: Downloading RELEASES file from https://storage.googleapis.com/osprey-downloads-c02f6a0d-347c-492b-a752-3e0651722e97/nest-win-x64
[01/06/25 09:39:18] info: FileDownloader: Downloading url: https://storage.googleapis.com/osprey-downloads-c02f6a0d-347c-492b-a752-3e0651722e97/nest-win-x64/RELEASES?id=AnthropicClaude&localVersion=0.9.3&arch=amd64
[01/06/25 09:39:18] info: Program: Finished Squirrel Updater
[01/06/25 12:11:26] info: Program: Starting Squirrel Updater: --checkForUpdate https://storage.googleapis.com/osprey-downloads-c02f6a0d-347c-492b-a752-3e0651722e97/nest-win-x64
[01/06/25 12:11:26] info: Program: Fetching update information, downloading from https://storage.googleapis.com/osprey-downloads-c02f6a0d-347c-492b-a752-3e0651722e97/nest-win-x64
[01/06/25 12:11:26] info: CheckForUpdateImpl: Using existing staging user ID: 9109ca86-fb15-5c28-8ff2-4c2cf134764d
[01/06/25 12:11:26] info: CheckForUpdateImpl: Downloading RELEASES file from https://storage.googleapis.com/osprey-downloads-c02f6a0d-347c-492b-a752-3e0651722e97/nest-win-x64
[01/06/25 12:11:26] info: FileDownloader: Downloading url: https://storage.googleapis.com/osprey-downloads-c02f6a0d-347c-492b-a752-3e0651722e97/nest-win-x64/RELEASES?id=AnthropicClaude&localVersion=0.9.3&arch=amd64
[01/06/25 12:11:27] info: Program: Finished Squirrel Updater
[01/06/25 16:02:22] info: Program: Starting Squirrel Updater: --checkForUpdate https://storage.googleapis.com/osprey-downloads-c02f6a0d-347c-492b-a752-3e0651722e97/nest-win-x64
[01/06/25 16:02:22] info: Program: Fetching update information, downloading from https://storage.googleapis.com/osprey-downloads-c02f6a0d-347c-492b-a752-3e0651722e97/nest-win-x64
[01/06/25 16:02:22] info: CheckForUpdateImpl: Using existing staging user ID: 9109ca86-fb15-5c28-8ff2-4c2cf134764d
[01/06/25 16:02:22] info: CheckForUpdateImpl: Downloading RELEASES file from https://storage.googleapis.com/osprey-downloads-c02f6a0d-347c-492b-a752-3e0651722e97/nest-win-x64
[01/06/25 16:02:22] info: FileDownloader: Downloading url: https://storage.googleapis.com/osprey-downloads-c02f6a0d-347c-492b-a752-3e0651722e97/nest-win-x64/RELEASES?id=AnthropicClaude&localVersion=0.9.3&arch=amd64
[01/06/25 16:02:23] info: Program: Finished Squirrel Updater
[01/06/25 20:37:27] info: Program: Starting Squirrel Updater: --checkForUpdate https://storage.googleapis.com/osprey-downloads-c02f6a0d-347c-492b-a752-3e0651722e97/nest-win-x64
[01/06/25 20:37:27] info: Program: Fetching update information, downloading from https://storage.googleapis.com/osprey-downloads-c02f6a0d-347c-492b-a752-3e0651722e97/nest-win-x64
[01/06/25 20:37:27] info: CheckForUpdateImpl: Using existing staging user ID: 9109ca86-fb15-5c28-8ff2-4c2cf134764d
[01/06/25 20:37:27] info: CheckForUpdateImpl: Downloading RELEASES file from https://storage.googleapis.com/osprey-downloads-c02f6a0d-347c-492b-a752-3e0651722e97/nest-win-x64
[01/06/25 20:37:28] info: FileDownloader: Downloading url: https://storage.googleapis.com/osprey-downloads-c02f6a0d-347c-492b-a752-3e0651722e97/nest-win-x64/RELEASES?id=AnthropicClaude&localVersion=0.9.3&arch=amd64
[01/06/25 20:37:28] info: Program: Finished Squirrel Updater
