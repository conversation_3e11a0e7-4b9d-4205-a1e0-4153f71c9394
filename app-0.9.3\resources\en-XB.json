{"+/cwsayrqk": [{"type": 0, "value": "‮"}, {"type": 0, "value": "∀ɔʇnɐʅ Sızǝ"}, {"type": 0, "value": "‬"}], "+7sd9hoyZA": [{"type": 0, "value": "‮"}, {"type": 0, "value": "Ↄodʎ"}, {"type": 0, "value": "‬"}], "/PgA81GVOD": [{"type": 0, "value": "‮"}, {"type": 0, "value": "Ǝpıʇ"}, {"type": 0, "value": "‬"}], "/bRGKhnXQ6": [{"type": 0, "value": "‮"}, {"type": 0, "value": "∀ uǝʍ ʌǝɹsıou ıs ɐʌɐıʅɐqʅǝ. Iʇ ʍıʅʅ qǝ poʍuʅoɐpǝp ɐup ıusʇɐʅʅǝp ɐnʇoɯɐʇıɔɐʅʅʎ."}, {"type": 0, "value": "‬"}], "0g8/VVdNuN": [{"type": 0, "value": "‮"}, {"type": 0, "value": "⅁o ʇo ɔʅɐnpǝ.ɐı/sǝʇʇıuƃs ʇo ɔouɟıƃnɹǝ ʎonɹ dɹoɟıʅǝ, ʇǝɐɯ, ɐup ɯoɹǝ."}, {"type": 0, "value": "‬"}], "0tZLEYF8mJ": [{"type": 0, "value": "‮"}, {"type": 0, "value": "ᗡǝʌǝʅodǝɹ"}, {"type": 0, "value": "‬"}], "1HUTYwndT2": [{"type": 0, "value": "‮"}, {"type": 0, "value": "Mıupoʍ"}, {"type": 0, "value": "‬"}], "1PfZLi/OV7": [{"type": 0, "value": "‮"}, {"type": 0, "value": "⅁ǝuǝɹɐʅ"}, {"type": 0, "value": "‬"}], "1TJUzU26sO": [{"type": 0, "value": "‮"}, {"type": 0, "value": "Snqɯıʇ Ⅎǝǝpqɐɔʞ…"}, {"type": 0, "value": "‬"}], "25aCMlTDUq": [{"type": 0, "value": "‮"}, {"type": 0, "value": "∀nʇoɯɐʇıɔɐʅʅʎ sʇɐɹʇ Ↄʅɐnpǝ ʍɥǝu ʎon ʅoƃ ıu"}, {"type": 0, "value": "‬"}], "3ML3xT+gEV": [{"type": 0, "value": "‮"}, {"type": 0, "value": "ᴚǝpo"}, {"type": 0, "value": "‬"}], "3gG1j3kRBX": [{"type": 0, "value": "‮"}, {"type": 0, "value": "Snqɯıʇ Ⅎǝǝpqɐɔʞ..."}, {"type": 0, "value": "‬"}], "3unrKzH4zB": [{"type": 0, "value": "‮"}, {"type": 0, "value": "Ↄodʎ"}, {"type": 0, "value": "‬"}], "4qP7MjrQfC": [{"type": 0, "value": "‮"}, {"type": 0, "value": "Ǝuʌıɹouɯǝuʇ ʌɐɹıɐqʅǝs"}, {"type": 0, "value": "‬"}], "5DUIVR3fVi": [{"type": 0, "value": "‮"}, {"type": 0, "value": "∀qonʇ..."}, {"type": 0, "value": "‬"}], "6yv8ytK4El": [{"type": 0, "value": "‮"}, {"type": 0, "value": "Ↄɥǝɔʞ ʎonɹ uǝʇʍoɹʞ ɔouuǝɔʇıou"}, {"type": 0, "value": "‬"}], "7fdcqxofEs": [{"type": 0, "value": "‮"}, {"type": 0, "value": "Ǝxıʇ"}, {"type": 0, "value": "‬"}], "7gSC+rZzXX": [{"type": 0, "value": "‮"}, {"type": 0, "value": "Ònıɔʞʅʎ odǝu Ↄʅɐnpǝ ɟɹoɯ ɐuʎʍɥǝɹǝ"}, {"type": 0, "value": "‬"}], "8YQEOfuaGO": [{"type": 0, "value": "‮"}, {"type": 0, "value": "Sǝʅǝɔʇ ∀ʅʅ"}, {"type": 0, "value": "‬"}], "9+afSO9e/t": [{"type": 0, "value": "‮"}, {"type": 0, "value": "Ⅎɐıʅǝp ʇo ɔɥǝɔʞ ɟoɹ ndpɐʇǝs: "}, {"type": 1, "value": "error"}, {"type": 0, "value": "‬"}], "9uNxNtcrFI": [{"type": 0, "value": "‮"}, {"type": 0, "value": "Iusʇɐʅʅ"}, {"type": 0, "value": "‬"}], "CZwl8X2D85": [{"type": 0, "value": "‮"}, {"type": 0, "value": "∀pʌɐuɔǝp odʇıous"}, {"type": 0, "value": "‬"}], "CizRPROPWo": [{"type": 0, "value": "‮"}, {"type": 0, "value": "Ↄʅɐnpǝ Hǝʅd"}, {"type": 0, "value": "‬"}], "D43DeqP+2t": [{"type": 0, "value": "‮"}, {"type": 0, "value": "Ↄʅɐnpǝ Sǝʇʇıuƃs"}, {"type": 0, "value": "‬"}], "D4DyT6MmPy": [{"type": 0, "value": "‮"}, {"type": 0, "value": "Ↄonʅp uoʇ ʅoɐp ɐdd sǝʇʇıuƃs"}, {"type": 0, "value": "‬"}], "DQTgg21B7g": [{"type": 0, "value": "‮"}, {"type": 0, "value": "Sɥoʍ ∀dd"}, {"type": 0, "value": "‬"}], "E9jYTa7AbX": [{"type": 0, "value": "‮"}, {"type": 0, "value": "Sʎsʇǝɯ ⊥ɹɐʎ"}, {"type": 0, "value": "‬"}], "EfdnINFnIz": [{"type": 0, "value": "‮"}, {"type": 0, "value": "Ⅎıʅǝ"}, {"type": 0, "value": "‬"}], "GSG5S0ysrR": [{"type": 0, "value": "‮"}, {"type": 0, "value": "ᴚnu ou Sʇɐɹʇnd"}, {"type": 0, "value": "‬"}], "HeHYq6bbS2": [{"type": 0, "value": "‮"}, {"type": 0, "value": "Ↄʅɐnpǝ ɔɐu ɹǝɔǝıʌǝ ıuɟoɹɯɐʇıou ʅıʞǝ dɹoɯdʇs ɐup ɐʇʇɐɔɥɯǝuʇs ɟɹoɯ sdǝɔıɐʅızǝp sǝɹʌǝɹs nsıuƃ Wopǝʅ Ↄouʇǝxʇ Ԁɹoʇoɔoʅ."}, {"type": 0, "value": "‬"}], "I5O68ogAtr": [{"type": 0, "value": "‮"}, {"type": 0, "value": "⅁ǝʇ Sʇɐɹʇǝp"}, {"type": 0, "value": "‬"}], "JVwNvMZjVT": [{"type": 0, "value": "‮"}, {"type": 0, "value": "Ԁɐsʇǝ"}, {"type": 0, "value": "‬"}], "KAo3lt5Hv+": [{"type": 0, "value": "‮"}, {"type": 0, "value": "Ԁɐsʇǝ"}, {"type": 0, "value": "‬"}], "Ko/2Ml7mZG": [{"type": 0, "value": "‮"}, {"type": 0, "value": "ᴚǝʅoɐp ⊥ɥıs Ԁɐƃǝ"}, {"type": 0, "value": "‬"}], "L32WRR6NOL": [{"type": 0, "value": "‮"}, {"type": 0, "value": "ᗡǝʅǝʇǝ"}, {"type": 0, "value": "‬"}], "L717supPIA": [{"type": 0, "value": "‮"}, {"type": 0, "value": "Sǝʇʇıuƃs"}, {"type": 0, "value": "‬"}], "LCWUQ/4Fu6": [{"type": 0, "value": "‮"}, {"type": 0, "value": "Ʌıǝʍ"}, {"type": 0, "value": "‬"}], "NZIwKxgxJ+": [{"type": 0, "value": "‮"}, {"type": 0, "value": "∀ɹǝ ʎon snɹǝ ʎon ʍɐuʇ ʇo ɹǝɯoʌǝ ʇɥǝ WↃԀ sǝɹʌǝɹ \""}, {"type": 1, "value": "server<PERSON>ey"}, {"type": 0, "value": "\"?"}, {"type": 0, "value": "‬"}], "Nmvo1ufAY5": [{"type": 0, "value": "‮"}, {"type": 0, "value": "Ↄonʅpu'ʇ ɔouuǝɔʇ ʇo Ↄʅɐnpǝ"}, {"type": 0, "value": "‬"}], "O3rtEd7aMd": [{"type": 0, "value": "‮"}, {"type": 0, "value": "Ⅎıup"}, {"type": 0, "value": "‬"}], "ODySlGptaj": [{"type": 0, "value": "‮"}, {"type": 0, "value": "Sǝʇʇıuƃs…"}, {"type": 0, "value": "‬"}], "PH29MShDiy": [{"type": 0, "value": "‮"}, {"type": 0, "value": "Ⅎoɹʍɐɹp"}, {"type": 0, "value": "‬"}], "PW5U8NgTto": [{"type": 0, "value": "‮"}, {"type": 0, "value": "Odǝu WↃԀ ⅂oƃ Ⅎıʅǝ..."}, {"type": 0, "value": "‬"}], "PZtcoAOSsa": [{"type": 0, "value": "‮"}, {"type": 0, "value": "ᗡou'ʇ Ǝuɐqʅǝ"}, {"type": 0, "value": "‬"}], "PbJ4jR0kv1": [{"type": 0, "value": "‮"}, {"type": 0, "value": "⅄on ɐɹǝ ɹnuuıuƃ ʇɥǝ ʅɐʇǝsʇ ʌǝɹsıou."}, {"type": 0, "value": "‬"}], "RTg057HE1D": [{"type": 0, "value": "‮"}, {"type": 0, "value": "Sɥoʍ ᗡǝʌ ⊥ooʅs"}, {"type": 0, "value": "‬"}], "S3MXlbjkax": [{"type": 0, "value": "‮"}, {"type": 0, "value": "Mɥɐʇ ɔɐu I ɥǝʅd ʎon ʍıʇɥ ʇopɐʎ?"}, {"type": 0, "value": "‬"}], "S3k5yXss2r": [{"type": 0, "value": "‮"}, {"type": 0, "value": "Ʌǝɹsıou "}, {"type": 1, "value": "version"}, {"type": 0, "value": "‬"}], "TH+W2Ad73P": [{"type": 0, "value": "‮"}, {"type": 0, "value": "Ↄnʇ"}, {"type": 0, "value": "‬"}], "UJCjEVPX6Q": [{"type": 0, "value": "‮"}, {"type": 0, "value": "⅂ooʞ ∩d"}, {"type": 0, "value": "‬"}], "Vvus2ifAny": [{"type": 0, "value": "‮"}, {"type": 0, "value": "Ǝpıʇ Ↄouɟıƃ"}, {"type": 0, "value": "‬"}], "W1pELwt/+a": [{"type": 0, "value": "‮"}, {"type": 0, "value": "Ↄʅɐnpǝ ɹnus ıu ʇɥǝ Noʇıɟıɔɐʇıou ∀ɹǝɐ"}, {"type": 0, "value": "‬"}], "WBvq3HlPae": [{"type": 0, "value": "‮"}, {"type": 0, "value": "Sǝʇ sɥoɹʇɔnʇ"}, {"type": 0, "value": "‬"}], "WF1HSu0jAC": [{"type": 0, "value": "‮"}, {"type": 0, "value": "Odǝu ⅂oƃs Ⅎoʅpǝɹ"}, {"type": 0, "value": "‬"}], "WZe86KSdrM": [{"type": 0, "value": "‮"}, {"type": 0, "value": "∀pp ʇo pıɔʇıouɐɹʎ"}, {"type": 0, "value": "‬"}], "WlhIx7DfFO": [{"type": 0, "value": "‮"}, {"type": 0, "value": "OӼ"}, {"type": 0, "value": "‬"}], "XPIoFTkh3e": [{"type": 0, "value": "‮"}, {"type": 0, "value": "No ∩dpɐʇǝ ∀ʌɐıʅɐqʅǝ"}, {"type": 0, "value": "‬"}], "XZ36+EBE5/": [{"type": 0, "value": "‮"}, {"type": 0, "value": "Zooɯ Onʇ"}, {"type": 0, "value": "‬"}], "XinCguXCgN": [{"type": 0, "value": "‮"}, {"type": 0, "value": "⅂ǝɐɹu ɯoɹǝ"}, {"type": 0, "value": "‬"}], "YTdYCYAf/Z": [{"type": 0, "value": "‮"}, {"type": 0, "value": "Sɥoʍ Ↄʅɐnpǝ ıu ʇɥǝ ɯǝun qɐɹ"}, {"type": 0, "value": "‬"}], "Z9g5m/V9Nq": [{"type": 0, "value": "‮"}, {"type": 0, "value": "Zooɯ Iu"}, {"type": 0, "value": "‬"}], "ZJZN1+KyJw": [{"type": 0, "value": "‮"}, {"type": 0, "value": "Sǝʇʇıuƃs..."}, {"type": 0, "value": "‬"}], "aNmxuDcWaU": [{"type": 0, "value": "‮"}, {"type": 0, "value": "Ǝɹɹoɹ"}, {"type": 0, "value": "‬"}], "aXdFLiVzjd": [{"type": 0, "value": "‮"}, {"type": 0, "value": "Sɥoʍ Wɐıu Mıupoʍ"}, {"type": 0, "value": "‬"}], "arbRxbtBkP": [{"type": 0, "value": "‮"}, {"type": 0, "value": "Ԑɐɔʞ"}, {"type": 0, "value": "‬"}], "baGq3gy8z1": [{"type": 0, "value": "‮"}, {"type": 0, "value": "Nǝʍ Ↄouʌǝɹsɐʇıou"}, {"type": 0, "value": "‬"}], "dKX0bpR+a2": [{"type": 0, "value": "‮"}, {"type": 0, "value": "Ònıʇ"}, {"type": 0, "value": "‬"}], "dLyz0Srosd": [{"type": 0, "value": "‮"}, {"type": 0, "value": "ᗡǝʌǝʅodǝɹ"}, {"type": 0, "value": "‬"}], "fEeEFfSz4K": [{"type": 0, "value": "‮"}, {"type": 0, "value": "Zooɯ Iu (ıupıǝ ɔooʅǝɹ ʌǝɹsıou)"}, {"type": 0, "value": "‬"}], "fFJxOwJRj2": [{"type": 0, "value": "‮"}, {"type": 0, "value": "∩upo"}, {"type": 0, "value": "‬"}], "fWDSQQgRO5": [{"type": 0, "value": "‮"}, {"type": 0, "value": "Wǝun Ԑɐɹ"}, {"type": 0, "value": "‬"}], "iFRmqBsr1N": [{"type": 0, "value": "‮"}, {"type": 0, "value": "∀qonʇ Ↄʅɐnpǝ"}, {"type": 0, "value": "‬"}], "ilE9e0uxNN": [{"type": 0, "value": "‮"}, {"type": 0, "value": "ᴚǝɟɹǝsɥ"}, {"type": 0, "value": "‬"}], "j66cdL4EK5": [{"type": 0, "value": "‮"}, {"type": 0, "value": "Odǝu ᗡoɔnɯǝuʇɐʇıou"}, {"type": 0, "value": "‬"}], "jd5ZNrRMNP": [{"type": 0, "value": "‮"}, {"type": 0, "value": "⅄on'ɹǝ ɐu ǝɐɹʅʎ ǝxdʅoɹǝɹ, so ʅǝʇ ns ʞuoʍ ʎonɹ ɟǝǝpqɐɔʞ."}, {"type": 0, "value": "‬"}], "k+06oXbIas": [{"type": 0, "value": "‮"}, {"type": 0, "value": "Sɥoʍ Ↄʅɐnpǝ ıu ʇɥǝ uoʇıɟıɔɐʇıous ɐɹǝɐ"}, {"type": 0, "value": "‬"}], "kYwW0OsI4M": [{"type": 0, "value": "‮"}, {"type": 0, "value": "Ↄʅɐnpǝ ᗡǝsʞʇod ıs ıu qǝʇɐ."}, {"type": 0, "value": "‬"}], "m3GfpKD1WX": [{"type": 0, "value": "‮"}, {"type": 0, "value": "ᴚǝʅoɐp"}, {"type": 0, "value": "‬"}], "mRXjxhS6p4": [{"type": 0, "value": "‮"}, {"type": 0, "value": "Ↄɥǝɔʞ ɟoɹ ∩dpɐʇǝs…"}, {"type": 0, "value": "‬"}], "ngLpGT7bUJ": [{"type": 0, "value": "‮"}, {"type": 0, "value": "Ↄonʅp uoʇ ʅoɐp pǝʌǝʅodǝɹ sǝʇʇıuƃs"}, {"type": 0, "value": "‬"}], "oQuOiX24pp": [{"type": 0, "value": "‮"}, {"type": 0, "value": "Ònıʇ"}, {"type": 0, "value": "‬"}], "pWXxZASpOB": [{"type": 0, "value": "‮"}, {"type": 0, "value": "Hǝʅd"}, {"type": 0, "value": "‬"}], "pgaCSv2/6H": [{"type": 0, "value": "‮"}, {"type": 0, "value": "∀ɹƃnɯǝuʇs"}, {"type": 0, "value": "‬"}], "q4hs14B00V": [{"type": 0, "value": "‮"}, {"type": 0, "value": "∩uʞuoʍu ǝɹɹoɹ"}, {"type": 0, "value": "‬"}], "rNAd+HxSK4": [{"type": 0, "value": "‮"}, {"type": 0, "value": "Odǝu WↃԀ ⅂oƃ Ⅎıʅǝ"}, {"type": 0, "value": "‬"}], "rY99UXvTDU": [{"type": 0, "value": "‮"}, {"type": 0, "value": "Ↄodʎ Iɯɐƃǝ"}, {"type": 0, "value": "‬"}], "rdiPpQVqvY": [{"type": 0, "value": "‮"}, {"type": 0, "value": "ᗡǝʌǝʅodǝɹ ɯopǝ ɐʅʅoʍs ɐɔɔǝss ʇo pǝʌǝʅodǝɹ ʇooʅs ɐup pǝqnƃƃıuƃ ɟǝɐʇnɹǝs. Ouʅʎ ǝuɐqʅǝ ʇɥıs ıɟ ʎon ʞuoʍ ʍɥɐʇ ʎon'ɹǝ poıuƃ."}, {"type": 0, "value": "‬"}], "rwFEudHXey": [{"type": 0, "value": "‮"}, {"type": 0, "value": "⊥ɥǝɹǝ ʍɐs ɐu ǝɹɹoɹ ɹǝɐpıuƃ oɹ dɐɹsıuƃ ɔʅɐnpǝ_pǝsʞʇod_ɔouɟıƃ.ɾsou: "}, {"type": 1, "value": "error"}, {"type": 0, "value": "‬"}], "sNnRQsIEYz": [{"type": 0, "value": "‮"}, {"type": 0, "value": "Ⅎıup ıu dɐƃǝ"}, {"type": 0, "value": "‬"}], "sZxWXq9BzJ": [{"type": 0, "value": "‮"}, {"type": 0, "value": "⅁ıʌǝ ɟǝǝpqɐɔʞ"}, {"type": 0, "value": "‬"}], "sys7RHphmL": [{"type": 0, "value": "‮"}, {"type": 0, "value": "Ⅎǝǝpqɐɔʞ"}, {"type": 0, "value": "‬"}], "tWutslc/9Z": [{"type": 0, "value": "‮"}, {"type": 0, "value": "Ǝuɐqʅǝ"}, {"type": 0, "value": "‬"}], "u1/hT7oRQY": [{"type": 0, "value": "‮"}, {"type": 0, "value": "Ònıɔʞ Ǝuʇɹʎ Ӽǝʎqoɐɹp Sɥoɹʇɔnʇ"}, {"type": 0, "value": "‬"}], "uc3dnSo+eo": [{"type": 0, "value": "‮"}, {"type": 0, "value": "Ⅎıʅǝ"}, {"type": 0, "value": "‬"}], "urCd4k/cE0": [{"type": 0, "value": "‮"}, {"type": 0, "value": "Ↄoɯɯɐup"}, {"type": 0, "value": "‬"}], "vgLHPxjh9O": [{"type": 0, "value": "‮"}, {"type": 0, "value": "Ǝuɐqʅǝ ᗡǝʌǝʅodǝɹ Wopǝ"}, {"type": 0, "value": "‬"}], "wS64bVG2CO": [{"type": 0, "value": "‮"}, {"type": 0, "value": "WↃԀ ıs ɐ dɹoʇoɔoʅ ʇɥɐʇ ǝuɐqʅǝs sǝɔnɹǝ ɔouuǝɔʇıous qǝʇʍǝǝu ɔʅıǝuʇs, snɔɥ ɐs ʇɥǝ Ↄʅɐnpǝ ᗡǝsʞʇod ɐdd, ɐup ʅoɔɐʅ sǝɹʌıɔǝs."}, {"type": 0, "value": "‬"}], "xJs1jZ8PoA": [{"type": 0, "value": "‮"}, {"type": 0, "value": "Ↄʅɐnpǝ ɹnus ıu ʇɥǝ qɐɔʞƃɹonup ǝʌǝu ʍɥǝu ʎon ɔʅosǝ ʇɥǝ ʍıupoʍ. Ↄʅıɔʞ ʇɥǝ Ↄʅɐnpǝ ıɔou ıu ʇɥǝ ʇɹɐʎ ʇo ɹǝodǝu ʇɥǝ ɐdd, oɹ ɹıƃɥʇ-ɔʅıɔʞ ʇo bnıʇ."}, {"type": 0, "value": "‬"}], "xKRKzVVy9c": [{"type": 0, "value": "‮"}, {"type": 0, "value": "Ↄouɟıƃnɹǝ"}, {"type": 0, "value": "‬"}], "xd436TVDRZ": [{"type": 0, "value": "‮"}, {"type": 0, "value": "⊥ɥǝɹǝ ʍɐs ɐu ǝɹɹoɹ ɹǝɐpıuƃ oɹ dɐɹsıuƃ pǝʌǝʅodǝɹ_sǝʇʇıuƃs.ɾsou: "}, {"type": 1, "value": "error"}, {"type": 0, "value": "‬"}], "y9tCbmRzHN": [{"type": 0, "value": "‮"}, {"type": 0, "value": "Ǝuɐqʅǝ ᗡǝʌǝʅodǝɹ Wopǝ?"}, {"type": 0, "value": "‬"}], "ytjMRobdyL": [{"type": 0, "value": "‮"}, {"type": 0, "value": "∩dpɐʇǝ ∀ʌɐıʅɐqʅǝ"}, {"type": 0, "value": "‬"}], "zAYm/Z684h": [{"type": 0, "value": "‮"}, {"type": 0, "value": "Hǝʅd"}, {"type": 0, "value": "‬"}], "zCIK9K8J4a": [{"type": 0, "value": "‮"}, {"type": 0, "value": "Ǝɹɹoɹ"}, {"type": 0, "value": "‬"}], "zSP70MVzIo": [{"type": 0, "value": "‮"}, {"type": 0, "value": "Ↄʅǝɐɹ sɥoɹʇɔnʇ"}, {"type": 0, "value": "‬"}]}