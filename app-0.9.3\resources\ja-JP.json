{"+/cwsayrqk": "実際のサイズ", "+7sd9hoyZA": "コピー", "/PgA81GVOD": "編集", "/bRGKhnXQ6": "新しいバージョンを利用可能です。自動的にダウンロード、インストールされます。", "0g8/VVdNuN": "プロフィール、チームなどの構成は claude.ai/settings で行えます。", "0tZLEYF8mJ": "開発者", "1HUTYwndT2": "ウィンドウ", "1PfZLi/OV7": "一般", "1TJUzU26sO": "フィードバックを送信…", "25aCMlTDUq": "ログイン時にクロードを自動的に起動します", "3ML3xT+gEV": "やり直し", "3gG1j3kRBX": "フィードバックを送信…", "3unrKzH4zB": "コピー", "4qP7MjrQfC": "環境変数", "5DUIVR3fVi": "バージョン情報...", "6yv8ytK4El": "ネットワーク接続をご確認ください。", "7fdcqxofEs": "終了", "7gSC+rZzXX": "どこからでもクロードをすばやく起動できます", "8YQEOfuaGO": "すべて選択", "9+afSO9e/t": "アップデートの確認に失敗しました：{error}", "9uNxNtcrFI": "インストール", "CZwl8X2D85": "詳細オプション", "CizRPROPWo": "<PERSON> ヘルプ", "D43DeqP+2t": "クロード設定", "D4DyT6MmPy": "アプリ設定を読み込めませんでした", "DQTgg21B7g": "アプリを表示", "E9jYTa7AbX": "システムトレイ", "EfdnINFnIz": "ファイル", "GSG5S0ysrR": "起動時に実行", "HeHYq6bbS2": "クロードはModel Context Protocolを使用して、専用サーバーからプロンプトや添付ファイルなどの情報を受け取ることができます。", "I5O68ogAtr": "はじめる", "JVwNvMZjVT": "貼り付け", "KAo3lt5Hv+": "貼り付け", "Ko/2Ml7mZG": "このページを再読み込み", "L32WRR6NOL": "削除", "L717supPIA": "設定", "LCWUQ/4Fu6": "表示", "NZIwKxgxJ+": "MCPサーバー「{serverKey}」を削除してもよろしいですか？", "Nmvo1ufAY5": "<PERSON>に接続できませんでした。", "O3rtEd7aMd": "検索", "ODySlGptaj": "設定…", "PH29MShDiy": "進む", "PW5U8NgTto": "MCP ログファイルを開く...", "PZtcoAOSsa": "有効にしない", "PbJ4jR0kv1": "最新バージョンを使用中です。", "RTg057HE1D": "開発者ツールを表示", "S3MXlbjkax": "本日はどのようなことでお手伝いできますでしょうか？", "S3k5yXss2r": "バージョン {version}", "TH+W2Ad73P": "切り取り", "UJCjEVPX6Q": "検索", "Vvus2ifAny": "構成を編集", "W1pELwt/+a": "クロードは通知領域で実行中です", "WBvq3HlPae": "ショートカットを設定", "WF1HSu0jAC": "ログフォルダを開く", "WZe86KSdrM": "辞書に追加", "WlhIx7DfFO": "OK", "XPIoFTkh3e": "アップデートはありません", "XZ36+EBE5/": "縮小", "XinCguXCgN": "詳細はこちら", "YTdYCYAf/Z": "メニューバーにクロードを表示する", "Z9g5m/V9Nq": "拡大", "ZJZN1+KyJw": "設定…", "aNmxuDcWaU": "エラー", "aXdFLiVzjd": "メインウィンドウを表示", "arbRxbtBkP": "戻る", "baGq3gy8z1": "新規会話", "dKX0bpR+a2": "終了", "dLyz0Srosd": "開発者", "fEeEFfSz4K": "拡大（別バージョン）", "fFJxOwJRj2": "元に戻す", "fWDSQQgRO5": "メニューバー", "iFRmqBsr1N": "Claude について", "ilE9e0uxNN": "更新", "j66cdL4EK5": "ドキュメントを開く", "jd5ZNrRMNP": "トライアルの初期参加者として、フィードバックをぜひご提供ください。", "k+06oXbIas": "通知領域にClaudeを表示する", "kYwW0OsI4M": "クロード デスクトップはベータ版です。", "m3GfpKD1WX": "再読み込み", "mRXjxhS6p4": "アップデートを確認...", "ngLpGT7bUJ": "開発者設定を読み込めませんでした", "oQuOiX24pp": "終了", "pWXxZASpOB": "ヘルプ", "pgaCSv2/6H": "引数", "q4hs14B00V": "不明なエラーが発生しました。", "rNAd+HxSK4": "MCP ログファイルを開く", "rY99UXvTDU": "画像をコピー", "rdiPpQVqvY": "開発者モードでは、開発ツールやデバッグ機能にアクセスできます。関連知識がある場合のみ有効にしてください。", "rwFEudHXey": "claude_desktop_config.json の読み込みまたは解析中にエラーが発生しました：{error}", "sNnRQsIEYz": "ページ内を検索", "sZxWXq9BzJ": "フィードバックを提供", "sys7RHphmL": "フィードバック", "tWutslc/9Z": "有効化", "u1/hT7oRQY": "クイック起動用キーボードショートカット", "uc3dnSo+eo": "ファイル", "urCd4k/cE0": "コマンド", "vgLHPxjh9O": "開発者モードを有効にする", "wS64bVG2CO": "MCPは、クロードデスクトップアプリなどのクライアントとローカルサービス間の安全な接続を可能にするプロトコルです。", "xJs1jZ8PoA": "クロードはウィンドウを閉じてもバックグラウンドで実行され続けます。再表示するにはトレイのクロードアイコンをクリックします。終了するには右クリックします。", "xKRKzVVy9c": "設定", "xd436TVDRZ": "developer_settings.json の読み込みまたは解析中にエラーが発生しました：{error}", "y9tCbmRzHN": "開発者モードを有効にしますか？", "ytjMRobdyL": "アップデートを利用可能", "zAYm/Z684h": "ヘルプ", "zCIK9K8J4a": "エラー", "zSP70MVzIo": "ショートカットを消去"}