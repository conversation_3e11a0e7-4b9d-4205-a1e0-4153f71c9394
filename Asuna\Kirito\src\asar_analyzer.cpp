#include <iostream>
#include <Windows.h>
#include <TlHelp32.h>
#include <vector>
#include <string>
#include <iomanip>
#include <fstream>

// ASAR Analysis Framework for <PERSON>.exe
// This tool uses the Yui kernel driver to perform deep analysis of ASAR integrity validation

namespace AsarAnalyzer {
    
    // Driver communication codes (must match <PERSON>i driver)
    namespace codes {
        constexpr ULONG attach = CTL_CODE(FILE_DEVICE_UNKNOWN, 0x696, METHOD_BUFFERED, FILE_SPECIAL_ACCESS);
        constexpr ULONG read = CTL_CODE(FILE_DEVICE_UNKNOWN, 0x697, METHOD_BUFFERED, FILE_SPECIAL_ACCESS);
        constexpr ULONG write = CTL_CODE(FILE_DEVICE_UNKNOWN, 0x698, METHOD_BUFFERED, FILE_SPECIAL_ACCESS);
        constexpr ULONG hook_api = CTL_CODE(FILE_DEVICE_UNKNOWN, 0x699, METHOD_BUFFERED, FILE_SPECIAL_ACCESS);
        constexpr ULONG read_pe_resource = CTL_CODE(FILE_DEVICE_UNKNOWN, 0x69A, METHOD_BUFFERED, FILE_SPECIAL_ACCESS);
        constexpr ULONG monitor_file_ops = CTL_CODE(FILE_DEVICE_UNKNOWN, 0x69B, METHOD_BUFFERED, FILE_SPECIAL_ACCESS);
        constexpr ULONG trace_calls = CTL_CODE(FILE_DEVICE_UNKNOWN, 0x69C, METHOD_BUFFERED, FILE_SPECIAL_ACCESS);
    }

    // Request structures (must match Yui driver)
    struct Request {
        HANDLE process_id;
        PVOID target;
        PVOID buffer;
        SIZE_T size;
        SIZE_T return_size;
    };

    struct AsarAnalysisRequest {
        HANDLE process_id;
        ULONG operation_type;
        PVOID target_address;
        PVOID buffer;
        SIZE_T buffer_size;
        WCHAR resource_type[64];
        WCHAR resource_name[64];
        ULONG_PTR function_address;
        BOOLEAN enable_monitoring;
    };

    // Key addresses from IDA analysis
    namespace ClaudeAddresses {
        constexpr ULONG_PTR BASE_ADDRESS = 0x140000000;
        
        // ASAR integrity validation functions
        constexpr ULONG_PTR IsEmbeddedAsarIntegrityValidationEnabled = 0x1403AC730;
        constexpr ULONG_PTR GetIntegrityForAsarArchive = 0x1403A99C0;
        constexpr ULONG_PTR GetIntegrityFromExecutableResources = 0x140429040;
        constexpr ULONG_PTR ValidateAsarBlockHash = 0x140337580;
        constexpr ULONG_PTR AsarFileValidator = 0x140337790;
        
        // String addresses for resource names
        constexpr ULONG_PTR ElectronAsarString = 0x1498D39E4;  // "ElectronAsar"
        constexpr ULONG_PTR IntegrityString = 0x1498CADE2;     // "Integrity"
        
        // ASAR loading functions
        constexpr ULONG_PTR IsOnlyLoadAppFromAsarEnabled = 0x1403C8560;
        constexpr ULONG_PTR AppSearchPaths = 0x1498CE2B6;
        constexpr ULONG_PTR AppSearchPathsOnlyLoadASAR = 0x1498CE2C5;
    }

    class AsarIntegrityAnalyzer {
    private:
        HANDLE driver_handle;
        DWORD target_pid;
        ULONG_PTR module_base;

    public:
        AsarIntegrityAnalyzer() : driver_handle(INVALID_HANDLE_VALUE), target_pid(0), module_base(0) {}

        bool Initialize() {
            // Connect to Yui driver
            driver_handle = CreateFile(L"\\\\.\\Asuna", GENERIC_READ, 0, nullptr, 
                                     OPEN_EXISTING, FILE_ATTRIBUTE_NORMAL, nullptr);
            if (driver_handle == INVALID_HANDLE_VALUE) {
                std::cout << "[-] Failed to connect to Yui driver\n";
                return false;
            }

            // Find Claude.exe process
            target_pid = GetProcessId(L"claude.exe");
            if (target_pid == 0) {
                std::cout << "[-] Failed to find claude.exe process\n";
                return false;
            }

            // Get module base address
            module_base = GetModuleBase(target_pid, L"claude.exe");
            if (module_base == 0) {
                std::cout << "[-] Failed to get claude.exe base address\n";
                return false;
            }

            // Attach to process
            if (!AttachToProcess(target_pid)) {
                std::cout << "[-] Failed to attach to claude.exe\n";
                return false;
            }

            std::cout << "[+] Successfully initialized ASAR analyzer\n";
            std::cout << "[+] Target PID: " << target_pid << "\n";
            std::cout << "[+] Module Base: 0x" << std::hex << module_base << "\n";
            return true;
        }

        void AnalyzeAsarIntegritySystem() {
            std::cout << "\n=== ASAR Integrity System Analysis ===\n";
            
            // 1. Check if integrity validation is enabled
            CheckIntegrityValidationStatus();
            
            // 2. Extract PE resource data
            ExtractIntegrityResources();
            
            // 3. Hook key functions for runtime analysis
            HookIntegrityFunctions();
            
            // 4. Monitor ASAR file operations
            MonitorAsarOperations();
            
            // 5. Trace hash validation calls
            TraceHashValidation();
        }

        void CheckIntegrityValidationStatus() {
            std::cout << "\n[*] Checking ASAR integrity validation status...\n";
            
            ULONG_PTR func_addr = module_base + (ClaudeAddresses::IsEmbeddedAsarIntegrityValidationEnabled - ClaudeAddresses::BASE_ADDRESS);
            
            // Read the function to see if it returns true/false
            BYTE function_bytes[32];
            if (ReadMemory(func_addr, function_bytes, sizeof(function_bytes))) {
                std::cout << "[+] IsEmbeddedAsarIntegrityValidationEnabled function bytes:\n";
                PrintHexDump(function_bytes, sizeof(function_bytes));
                
                // Look for return value patterns
                AnalyzeFunctionReturnValue(function_bytes, sizeof(function_bytes));
            }
        }

        void ExtractIntegrityResources() {
            std::cout << "\n[*] Extracting PE resource integrity data...\n";
            
            AsarAnalysisRequest req = {};
            req.process_id = reinterpret_cast<HANDLE>(target_pid);
            req.operation_type = 1; // Extract PE resources
            wcscpy_s(req.resource_type, L"ElectronAsar");
            wcscpy_s(req.resource_name, L"Integrity");
            
            BYTE resource_buffer[4096];
            req.buffer = resource_buffer;
            req.buffer_size = sizeof(resource_buffer);
            
            if (DeviceIoControl(driver_handle, codes::read_pe_resource, &req, sizeof(req), 
                              &req, sizeof(req), nullptr, nullptr)) {
                std::cout << "[+] Successfully extracted integrity resource data\n";
                
                // Save to file for analysis
                std::ofstream file("integrity_data.json", std::ios::binary);
                if (file.is_open()) {
                    file.write(reinterpret_cast<char*>(resource_buffer), req.buffer_size);
                    file.close();
                    std::cout << "[+] Integrity data saved to integrity_data.json\n";
                }
                
                // Try to parse as JSON
                ParseIntegrityData(resource_buffer, req.buffer_size);
            }
        }

        void HookIntegrityFunctions() {
            std::cout << "\n[*] Setting up hooks for integrity functions...\n";
            
            std::vector<ULONG_PTR> functions_to_hook = {
                ClaudeAddresses::IsEmbeddedAsarIntegrityValidationEnabled,
                ClaudeAddresses::GetIntegrityForAsarArchive,
                ClaudeAddresses::ValidateAsarBlockHash,
                ClaudeAddresses::AsarFileValidator
            };
            
            for (auto func_rva : functions_to_hook) {
                ULONG_PTR func_addr = module_base + (func_rva - ClaudeAddresses::BASE_ADDRESS);
                
                AsarAnalysisRequest req = {};
                req.process_id = reinterpret_cast<HANDLE>(target_pid);
                req.operation_type = 2; // Hook function
                req.function_address = func_addr;
                req.enable_monitoring = TRUE;
                
                if (DeviceIoControl(driver_handle, codes::hook_api, &req, sizeof(req), 
                                  &req, sizeof(req), nullptr, nullptr)) {
                    std::cout << "[+] Hooked function at 0x" << std::hex << func_addr << "\n";
                }
            }
        }

        void MonitorAsarOperations() {
            std::cout << "\n[*] Monitoring ASAR file operations...\n";
            
            AsarAnalysisRequest req = {};
            req.process_id = reinterpret_cast<HANDLE>(target_pid);
            req.operation_type = 3; // Monitor file operations
            req.enable_monitoring = TRUE;
            
            DeviceIoControl(driver_handle, codes::monitor_file_ops, &req, sizeof(req), 
                          &req, sizeof(req), nullptr, nullptr);
        }

        void TraceHashValidation() {
            std::cout << "\n[*] Starting hash validation tracing...\n";
            std::cout << "[*] Press ESC to stop tracing...\n";
            
            while (!(GetAsyncKeyState(VK_ESCAPE) & 0x8000)) {
                // Check for hash validation events
                AsarAnalysisRequest req = {};
                req.process_id = reinterpret_cast<HANDLE>(target_pid);
                req.operation_type = 4; // Get trace data
                
                BYTE trace_buffer[1024];
                req.buffer = trace_buffer;
                req.buffer_size = sizeof(trace_buffer);
                
                if (DeviceIoControl(driver_handle, codes::trace_calls, &req, sizeof(req), 
                                  &req, sizeof(req), nullptr, nullptr)) {
                    if (req.buffer_size > 0) {
                        std::cout << "[TRACE] Hash validation event detected\n";
                        PrintHexDump(trace_buffer, req.buffer_size);
                    }
                }
                
                Sleep(100);
            }
        }

    private:
        DWORD GetProcessId(const wchar_t* process_name) {
            DWORD process_id = 0;
            HANDLE snap_shot = CreateToolhelp32Snapshot(TH32CS_SNAPPROCESS, NULL);
            if (snap_shot == INVALID_HANDLE_VALUE) return process_id;

            PROCESSENTRY32W entry = {};
            entry.dwSize = sizeof(decltype(entry));

            if (Process32FirstW(snap_shot, &entry) == TRUE) {
                if (_wcsicmp(process_name, entry.szExeFile) == 0)
                    process_id = entry.th32ProcessID;
                else {
                    while (Process32NextW(snap_shot, &entry) == TRUE) {
                        if (_wcsicmp(process_name, entry.szExeFile) == 0) {
                            process_id = entry.th32ProcessID;
                            break;
                        }
                    }
                }
            }
            CloseHandle(snap_shot);
            return process_id;
        }

        ULONG_PTR GetModuleBase(DWORD pid, const wchar_t* module_name) {
            ULONG_PTR module_base = 0;
            HANDLE snap_shot = CreateToolhelp32Snapshot(TH32CS_SNAPMODULE | TH32CS_SNAPMODULE32, pid);
            if (snap_shot == INVALID_HANDLE_VALUE) return module_base;

            MODULEENTRY32W entry = {};
            entry.dwSize = sizeof(decltype(entry));

            if (Module32FirstW(snap_shot, &entry) == TRUE) {
                if (wcsstr(module_name, entry.szModule) != nullptr)
                    module_base = reinterpret_cast<ULONG_PTR>(entry.modBaseAddr);
                else {
                    while (Module32NextW(snap_shot, &entry) == TRUE) {
                        if (wcsstr(module_name, entry.szModule) != nullptr) {
                            module_base = reinterpret_cast<ULONG_PTR>(entry.modBaseAddr);
                            break;
                        }
                    }
                }
            }
            CloseHandle(snap_shot);
            return module_base;
        }

        bool AttachToProcess(DWORD pid) {
            Request r;
            r.process_id = reinterpret_cast<HANDLE>(pid);
            return DeviceIoControl(driver_handle, codes::attach, &r, sizeof(r), &r, sizeof(r), nullptr, nullptr);
        }

        bool ReadMemory(ULONG_PTR addr, void* buffer, SIZE_T size) {
            Request r;
            r.target = reinterpret_cast<PVOID>(addr);
            r.buffer = buffer;
            r.size = size;
            return DeviceIoControl(driver_handle, codes::read, &r, sizeof(r), &r, sizeof(r), nullptr, nullptr);
        }

        void PrintHexDump(const BYTE* data, SIZE_T size) {
            for (SIZE_T i = 0; i < size; i += 16) {
                std::cout << std::hex << std::setfill('0') << std::setw(8) << i << ": ";
                
                for (SIZE_T j = 0; j < 16 && (i + j) < size; j++) {
                    std::cout << std::hex << std::setfill('0') << std::setw(2) << static_cast<int>(data[i + j]) << " ";
                }
                std::cout << "\n";
            }
        }

        void AnalyzeFunctionReturnValue(const BYTE* bytes, SIZE_T size) {
            // Look for common return patterns
            for (SIZE_T i = 0; i < size - 1; i++) {
                if (bytes[i] == 0xB0) { // mov al, imm8
                    std::cout << "[+] Found return value: " << static_cast<int>(bytes[i + 1]) << "\n";
                } else if (bytes[i] == 0x33 && bytes[i + 1] == 0xC0) { // xor eax, eax
                    std::cout << "[+] Function returns 0 (FALSE)\n";
                } else if (bytes[i] == 0xB8) { // mov eax, imm32
                    DWORD value = *reinterpret_cast<const DWORD*>(&bytes[i + 1]);
                    std::cout << "[+] Function returns: 0x" << std::hex << value << "\n";
                }
            }
        }

        void ParseIntegrityData(const BYTE* data, SIZE_T size) {
            std::cout << "[*] Attempting to parse integrity data as JSON...\n";
            
            // Look for JSON patterns
            std::string str_data(reinterpret_cast<const char*>(data), size);
            
            if (str_data.find("{") != std::string::npos || str_data.find("[") != std::string::npos) {
                std::cout << "[+] Found JSON-like structure:\n";
                std::cout << str_data.substr(0, std::min(size, static_cast<SIZE_T>(500))) << "\n";
            } else {
                std::cout << "[*] Data doesn't appear to be JSON, showing hex dump:\n";
                PrintHexDump(data, std::min(size, static_cast<SIZE_T>(256)));
            }
        }
    };
}

int main() {
    std::cout << "=== ASAR Integrity Analysis Tool ===\n";
    std::cout << "Analyzing Claude.exe ASAR integrity validation system\n\n";

    AsarAnalyzer::AsarIntegrityAnalyzer analyzer;
    
    if (!analyzer.Initialize()) {
        std::cout << "[-] Failed to initialize analyzer\n";
        std::cin.get();
        return 1;
    }

    analyzer.AnalyzeAsarIntegritySystem();

    std::cout << "\n[*] Analysis complete. Press Enter to exit...\n";
    std::cin.get();
    return 0;
}
