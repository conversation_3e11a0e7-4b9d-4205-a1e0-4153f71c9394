﻿  Building 'Yui' with toolset 'WindowsKernelModeDriver10.0' and the 'Universal' target platform.
C:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Microsoft\VC\v170\Microsoft.CppBuild.targets(523,5): warning MSB8004: Intermediate Directory does not end with a trailing slash.  This build instance will add the slash as it is required to allow proper evaluation of the Intermediate Directory.
  Stamping C:\Users\<USER>\AppData\Local\AnthropicClaude\Asuna\build\Yui_intermediates\Yui.inf
  Stamping [Version] section with DriverVer=06/02/2025,0.2.48.321
  main.cpp
WdfDriverEntry.lib(stub.obj) : error LNK2019: unresolved external symbol DriverEntry referenced in function FxDriverEntryWorker
    Hint on symbols that are defined and could potentially match:
      "long __cdecl DriverEntry(void)" (?DriverEntry@@YAJXZ)
C:\Users\<USER>\AppData\Local\AnthropicClaude\Asuna\build\Yui.sys : fatal error LNK1120: 1 unresolved externals
