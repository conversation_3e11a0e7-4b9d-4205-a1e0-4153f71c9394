﻿  Building 'Yui' with toolset 'WindowsKernelModeDriver10.0' and the 'Universal' target platform.
C:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Microsoft\VC\v170\Microsoft.CppBuild.targets(523,5): warning MSB8004: Intermediate Directory does not end with a trailing slash.  This build instance will add the slash as it is required to allow proper evaluation of the Intermediate Directory.
  Stamping C:\Users\<USER>\AppData\Local\AnthropicClaude\Asuna\build\Yui_intermediates\Yui.inf
  Stamping [Version] section with DriverVer=06/02/2025,0.5.42.913
  Yui.vcxproj -> C:\Users\<USER>\AppData\Local\AnthropicClaude\Asuna\build\Yui.sys
  Done Adding Additional Store
  Successfully signed: C:\Users\<USER>\AppData\Local\AnthropicClaude\Asuna\build\Yui.sys
  
  Driver is 'Universal'.
  ........................
  Signability test complete.
  
  Errors:
  None
  
  Warnings:
  None
  
  Catalog generation complete.
  C:\Users\<USER>\AppData\Local\AnthropicClaude\Asuna\build\Yui\yui.cat
  Done Adding Additional Store
  Successfully signed: C:\Users\<USER>\AppData\Local\AnthropicClaude\Asuna\build\Yui\yui.cat
  
