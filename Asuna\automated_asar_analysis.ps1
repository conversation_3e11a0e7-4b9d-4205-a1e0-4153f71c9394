# Automated ASAR Integrity Analysis Script
# This script automates the entire ASAR analysis workflow using host terminal

param(
    [Parameter(Mandatory=$false)]
    [string]$VMName = "KernelDevelopment",
    
    [Parameter(Mandatory=$false)]
    [string]$ComPipe = "\\.\pipe\com_1",
    
    [Parameter(Mandatory=$false)]
    [string]$LogDirectory = "C:\Analysis\Logs",
    
    [Parameter(Mandatory=$false)]
    [int]$AnalysisTimeoutMinutes = 10
)

Write-Host "=== Automated ASAR Integrity Analysis ===" -ForegroundColor Cyan
Write-Host "Target VM: $VMName" -ForegroundColor Yellow
Write-Host "COM Pipe: $ComPipe" -ForegroundColor Yellow

# Create log directory
if (-not (Test-Path $LogDirectory)) {
    New-Item -ItemType Directory -Path $LogDirectory -Force | Out-Null
    Write-Host "Created log directory: $LogDirectory" -ForegroundColor Green
}

$timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
$logFile = "$LogDirectory\asar_analysis_$timestamp.log"
$windbgScript = "$LogDirectory\windbg_commands_$timestamp.txt"

# Function to write timestamped log
function Write-Log {
    param([string]$Message, [string]$Level = "INFO")
    $time = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logEntry = "[$time] [$Level] $Message"
    Write-Host $logEntry
    Add-Content -Path $logFile -Value $logEntry
}

# Function to check if VM is running
function Test-VMRunning {
    param([string]$VMName)
    try {
        $vm = Get-VM -Name $VMName -ErrorAction Stop
        return $vm.State -eq "Running"
    } catch {
        return $false
    }
}

# Function to start VM if not running
function Start-VMIfNeeded {
    param([string]$VMName)
    if (-not (Test-VMRunning -VMName $VMName)) {
        Write-Log "Starting VM: $VMName"
        Start-VM -Name $VMName
        
        # Wait for VM to boot
        Write-Log "Waiting for VM to boot..."
        Start-Sleep -Seconds 30
        
        # Wait for VM to be responsive
        $timeout = 120
        $elapsed = 0
        while ($elapsed -lt $timeout) {
            if (Test-VMRunning -VMName $VMName) {
                Write-Log "VM is running"
                break
            }
            Start-Sleep -Seconds 5
            $elapsed += 5
        }
    } else {
        Write-Log "VM is already running"
    }
}

# Function to create WinDbg automation script
function Create-WinDbgScript {
    param([string]$ScriptPath)
    
    $windbgCommands = @"
.echo "=== Automated ASAR Analysis Started ==="
.logopen $logFile
.ttime

.echo "=== Setting up ASAR breakpoints ==="
bp claude+0x3AC730 ".echo 'ASAR: IsEmbeddedAsarIntegrityValidationEnabled'; r rax; k 5; g"
bp claude+0x3A99C0 ".echo 'ASAR: GetIntegrityForAsarArchive'; r rcx,rdx,r8; k 5; g"
bp claude+0x429040 ".echo 'ASAR: GetIntegrityFromExecutableResources'; r rcx,rdx; k 5; g"
bp claude+0x337580 ".echo 'ASAR: ValidateAsarBlockHash'; r rcx,rdx,r8; k 5; g"
bp claude+0x337790 ".echo 'ASAR: AsarFileValidator'; r rcx,rdx; k 5; g"

.echo "=== Setting up API breakpoints ==="
bp kernel32!FindResourceW ".echo 'API: FindResourceW'; .if (@rdx != 0) { du @rdx } .if (@r8 != 0) { du @r8 } g"
bp kernel32!LoadResource ".echo 'API: LoadResource'; r rcx,rdx; g"
bp kernel32!LockResource ".echo 'API: LockResource'; r rcx; g"
bp kernel32!CreateFileW ".echo 'API: CreateFileW'; .if (@rcx != 0) { du @rcx } g"
bp kernel32!ReadFile ".echo 'API: ReadFile'; r rcx,rdx,r8; g"

.echo "=== Setting up crypto breakpoints ==="
bp bcrypt!BCryptCreateHash ".echo 'CRYPTO: BCryptCreateHash'; r rcx,rdx; g"
bp bcrypt!BCryptHashData ".echo 'CRYPTO: BCryptHashData'; r rcx,rdx,r8; g"
bp bcrypt!BCryptFinishHash ".echo 'CRYPTO: BCryptFinishHash'; r rcx,rdx,r8; g"

.echo "=== Searching for ASAR strings ==="
s -a 0 L?0x7fffffff "app.asar"
s -a 0 L?0x7fffffff "ElectronAsar"
s -a 0 L?0x7fffffff "Integrity"
s -a 0 L?0x7fffffff "sha256"
s -u 0 L?0x7fffffff "app.asar"

.echo "=== Starting execution monitoring ==="
g

.echo "=== Analysis setup complete - monitoring for $AnalysisTimeoutMinutes minutes ==="
"@

    Set-Content -Path $ScriptPath -Value $windbgCommands
    Write-Log "Created WinDbg script: $ScriptPath"
}

# Function to start WinDbg with automation
function Start-WinDbgAnalysis {
    param([string]$ComPipe, [string]$ScriptPath)
    
    Write-Log "Starting WinDbg with COM pipe: $ComPipe"
    
    # Create WinDbg startup script
    Create-WinDbgScript -ScriptPath $ScriptPath
    
    # Start WinDbg in background
    $windbgArgs = @(
        "-k", "com:pipe,port=$ComPipe,resets=0,reconnect"
        "-c", ".scriptload `"$ScriptPath`""
    )
    
    Write-Log "Starting WinDbg with arguments: $($windbgArgs -join ' ')"
    
    try {
        $windbgProcess = Start-Process -FilePath "windbg" -ArgumentList $windbgArgs -PassThru -WindowStyle Minimized
        Write-Log "WinDbg started with PID: $($windbgProcess.Id)"
        return $windbgProcess
    } catch {
        Write-Log "Failed to start WinDbg: $_" -Level "ERROR"
        return $null
    }
}

# Function to execute commands in VM via PowerShell Direct
function Invoke-VMCommand {
    param([string]$VMName, [string]$Command, [string]$WorkingDirectory = "C:\Tools")
    
    try {
        Write-Log "Executing in VM: $Command"
        
        # Use PowerShell Direct to execute command in VM
        $session = New-PSSession -VMName $VMName -Credential (Get-Credential -Message "Enter VM credentials")
        
        $result = Invoke-Command -Session $session -ScriptBlock {
            param($cmd, $workDir)
            Set-Location $workDir
            Invoke-Expression $cmd
        } -ArgumentList $Command, $WorkingDirectory
        
        Remove-PSSession $session
        
        Write-Log "VM command completed: $Command"
        return $result
    } catch {
        Write-Log "Failed to execute VM command: $_" -Level "ERROR"
        return $null
    }
}

# Function to start Claude.exe in VM
function Start-ClaudeInVM {
    param([string]$VMName)
    
    Write-Log "Starting Claude.exe in VM"
    
    # Start Claude.exe asynchronously
    $claudeCommand = "Start-Process -FilePath 'C:\Tools\claude.exe' -WorkingDirectory 'C:\Tools' -PassThru"
    $result = Invoke-VMCommand -VMName $VMName -Command $claudeCommand
    
    if ($result) {
        Write-Log "Claude.exe started in VM"
    } else {
        Write-Log "Failed to start Claude.exe in VM" -Level "ERROR"
    }
}

# Function to start ASAR analyzer in VM
function Start-AsarAnalyzerInVM {
    param([string]$VMName)
    
    Write-Log "Starting ASAR analyzer in VM"
    
    # Start ASAR analyzer asynchronously
    $analyzerCommand = "Start-Process -FilePath 'C:\Tools\asar_analyzer.exe' -WorkingDirectory 'C:\Tools' -PassThru"
    $result = Invoke-VMCommand -VMName $VMName -Command $analyzerCommand
    
    if ($result) {
        Write-Log "ASAR analyzer started in VM"
    } else {
        Write-Log "Failed to start ASAR analyzer in VM" -Level "ERROR"
    }
}

# Function to monitor analysis progress
function Monitor-Analysis {
    param([int]$TimeoutMinutes, [System.Diagnostics.Process]$WinDbgProcess)
    
    Write-Log "Monitoring analysis for $TimeoutMinutes minutes"
    
    $endTime = (Get-Date).AddMinutes($TimeoutMinutes)
    
    while ((Get-Date) -lt $endTime) {
        # Check if WinDbg is still running
        if ($WinDbgProcess -and $WinDbgProcess.HasExited) {
            Write-Log "WinDbg process has exited" -Level "WARNING"
            break
        }
        
        # Check log file for interesting events
        if (Test-Path $logFile) {
            $recentLines = Get-Content $logFile -Tail 10
            foreach ($line in $recentLines) {
                if ($line -match "ASAR:|CRYPTO:|API:") {
                    Write-Log "Detected: $line" -Level "ANALYSIS"
                }
            }
        }
        
        Start-Sleep -Seconds 30
    }
    
    Write-Log "Analysis monitoring completed"
}

# Function to generate analysis report
function Generate-AnalysisReport {
    param([string]$LogFile)
    
    $reportFile = $LogFile -replace "\.log$", "_report.txt"
    
    Write-Log "Generating analysis report: $reportFile"
    
    $report = @"
=== ASAR Integrity Analysis Report ===
Generated: $(Get-Date)
Log File: $LogFile

=== Summary ===
"@

    if (Test-Path $LogFile) {
        $logContent = Get-Content $LogFile
        
        # Count different types of events
        $asarEvents = ($logContent | Where-Object { $_ -match "ASAR:" }).Count
        $cryptoEvents = ($logContent | Where-Object { $_ -match "CRYPTO:" }).Count
        $apiEvents = ($logContent | Where-Object { $_ -match "API:" }).Count
        
        $report += @"

ASAR Function Calls: $asarEvents
Crypto Operations: $cryptoEvents  
API Calls: $apiEvents

=== Detailed Events ===
"@
        
        # Add interesting events to report
        $interestingEvents = $logContent | Where-Object { $_ -match "ASAR:|CRYPTO:|API:" }
        $report += ($interestingEvents -join "`n")
    }
    
    Set-Content -Path $reportFile -Value $report
    Write-Log "Analysis report generated: $reportFile"
    
    return $reportFile
}

# Main execution flow
try {
    Write-Log "Starting automated ASAR analysis"
    
    # Step 1: Ensure VM is running
    Start-VMIfNeeded -VMName $VMName
    
    # Step 2: Start WinDbg with automation
    $windbgProcess = Start-WinDbgAnalysis -ComPipe $ComPipe -ScriptPath $windbgScript
    
    if (-not $windbgProcess) {
        throw "Failed to start WinDbg"
    }
    
    # Step 3: Wait for WinDbg to initialize
    Write-Log "Waiting for WinDbg to initialize..."
    Start-Sleep -Seconds 10
    
    # Step 4: Start Claude.exe in VM
    Start-ClaudeInVM -VMName $VMName
    
    # Step 5: Wait for Claude to load
    Write-Log "Waiting for Claude to load..."
    Start-Sleep -Seconds 15
    
    # Step 6: Start ASAR analyzer in VM
    Start-AsarAnalyzerInVM -VMName $VMName
    
    # Step 7: Monitor analysis
    Monitor-Analysis -TimeoutMinutes $AnalysisTimeoutMinutes -WinDbgProcess $windbgProcess
    
    # Step 8: Generate report
    $reportFile = Generate-AnalysisReport -LogFile $logFile
    
    Write-Log "Automated analysis completed successfully"
    Write-Log "Log file: $logFile"
    Write-Log "Report file: $reportFile"
    
} catch {
    Write-Log "Automated analysis failed: $_" -Level "ERROR"
} finally {
    # Cleanup
    if ($windbgProcess -and -not $windbgProcess.HasExited) {
        Write-Log "Stopping WinDbg process"
        $windbgProcess.Kill()
    }
}

Write-Host "`n=== Analysis Complete ===" -ForegroundColor Green
Write-Host "Check the following files for results:" -ForegroundColor Cyan
Write-Host "  Log: $logFile" -ForegroundColor White
Write-Host "  Report: $($logFile -replace '\.log$', '_report.txt')" -ForegroundColor White
