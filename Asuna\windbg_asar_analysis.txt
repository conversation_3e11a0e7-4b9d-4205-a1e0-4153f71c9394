$$
$$ WinDbg Script for ASAR Integrity Analysis
$$ Usage: .scriptload windbg_asar_analysis.txt
$$

.echo "=== ASAR Integrity Analysis WinDbg Script ==="
.echo "Setting up breakpoints and analysis for Claude.exe ASAR system"

$$ Set up symbols if not already loaded
.sympath+ srv*c:\symbols*https://msdl.microsoft.com/download/symbols
.reload

$$ Find claude.exe process
!process 0 0 claude.exe

$$ Key function addresses (adjust based on your IDA analysis)
$$ These are RVAs from base 0x140000000

$$ ASAR Integrity Functions
bp claude+0x3AC730 ".echo 'IsEmbeddedAsarIntegrityValidationEnabled called'; r; k; g"
bp claude+0x3A99C0 ".echo 'GetIntegrityForAsarArchive called'; r; k; g"
bp claude+0x429040 ".echo 'GetIntegrityFromExecutableResources called'; r; k; g"
bp claude+0x337580 ".echo 'ValidateAsarBlockHash called'; r; k; g"
bp claude+0x337790 ".echo 'AsarFileValidator called'; r; k; g"

$$ ASAR Loading Functions
bp claude+0x3C8560 ".echo 'IsOnlyLoadAppFromAsarEnabled called'; r; k; g"

$$ Windows API hooks for resource access
bp kernel32!FindResourceW ".echo 'FindResourceW called'; du @rdx; du @r8; r; k; g"
bp kernel32!LoadResource ".echo 'LoadResource called'; r; k; g"
bp kernel32!LockResource ".echo 'LockResource called'; r; k; g"

$$ File operation hooks
bp kernel32!CreateFileW ".echo 'CreateFileW called'; du @rcx; r; k; g"
bp kernel32!ReadFile ".echo 'ReadFile called'; r; k; g"

$$ Crypto API hooks (if used)
bp bcrypt!BCryptCreateHash ".echo 'BCryptCreateHash called'; r; k; g"
bp bcrypt!BCryptHashData ".echo 'BCryptHashData called'; r; k; g"
bp bcrypt!BCryptFinishHash ".echo 'BCryptFinishHash called'; r; k; g"

$$ Memory allocation hooks
bp ntdll!RtlAllocateHeap ".echo 'RtlAllocateHeap called'; r; k; g"

.echo "Breakpoints set. Use 'g' to continue execution."
.echo ""
.echo "Useful commands:"
.echo "  !heap -a          - Analyze heap"
.echo "  !address          - Show memory layout"
.echo "  lm                - List loaded modules"
.echo "  .process          - Show current process"
.echo "  dt nt!_PEB @$peb  - Show Process Environment Block"
.echo ""
.echo "ASAR-specific analysis:"
.echo "  x claude!*asar*   - Find ASAR-related symbols"
.echo "  x claude!*integrity* - Find integrity-related symbols"
.echo "  s -a 0 L?0x7fffffff \"app.asar\" - Search for ASAR strings"
.echo "  s -a 0 L?0x7fffffff \"ElectronAsar\" - Search for resource type"
.echo "  s -a 0 L?0x7fffffff \"Integrity\" - Search for resource name"

$$ Custom function to dump PE resources
.block
{
    .echo "=== PE Resource Analysis ==="
    
    $$ Get PEB
    r $t0 = @$peb
    
    $$ Get image base
    r $t1 = poi(@$peb + 0x10)
    .printf "Image base: 0x%p\n", @$t1
    
    $$ Parse PE headers to find resource section
    r $t2 = @$t1 + poi(@$t1 + 0x3c)  $$ NT headers
    .printf "NT Headers: 0x%p\n", @$t2
    
    $$ Get number of sections
    r $t3 = wo(@$t2 + 0x6)
    .printf "Number of sections: %d\n", @$t3
    
    $$ Find .rsrc section
    r $t4 = @$t2 + 0xf8  $$ First section header
    .for (r $t5 = 0; @$t5 < @$t3; r $t5 = @$t5 + 1)
    {
        $$ Check if this is .rsrc section
        .if (dwo(@$t4) == 0x6372732e)  $$ ".rsr" in little endian
        {
            .printf "Found .rsrc section at offset 0x%x\n", @$t4
            r $t6 = dwo(@$t4 + 0x14)  $$ Raw data offset
            r $t7 = dwo(@$t4 + 0x10)  $$ Raw data size
            .printf "Resource data: offset=0x%x, size=0x%x\n", @$t6, @$t7
            
            $$ Dump first 256 bytes of resource data
            db @$t1 + @$t6 L100
        }
        r $t4 = @$t4 + 0x28  $$ Next section header
    }
}

$$ Function to analyze ASAR integrity data
.block
{
    .echo "=== ASAR Integrity Data Analysis ==="
    
    $$ Search for "ElectronAsar" string in memory
    s -a 0 L?0x7fffffff "ElectronAsar"
    
    $$ Search for "Integrity" string
    s -a 0 L?0x7fffffff "Integrity"
    
    $$ Search for JSON-like patterns
    s -a 0 L?0x7fffffff "\"file\""
    s -a 0 L?0x7fffffff "\"alg\""
    s -a 0 L?0x7fffffff "\"value\""
    s -a 0 L?0x7fffffff "sha256"
    
    $$ Search for base64-encoded data patterns
    s -a 0 L?0x7fffffff "eyJ"  $$ Common base64 start for JSON
}

$$ Function to trace ASAR function calls
.block
{
    .echo "=== Setting up ASAR function tracing ==="
    
    $$ Enable tracing
    .ttime
    
    $$ Set conditional breakpoints with detailed logging
    bp claude+0x3AC730 ".printf 'IsEmbeddedAsarIntegrityValidationEnabled: RCX=%p, RDX=%p\n', @rcx, @rdx; .printf 'Stack: '; dps @rsp L8; g"
    
    bp claude+0x3A99C0 ".printf 'GetIntegrityForAsarArchive: RCX=%p, RDX=%p, R8=%p\n', @rcx, @rdx, @r8; .printf 'Stack: '; dps @rsp L8; g"
    
    bp claude+0x429040 ".printf 'GetIntegrityFromExecutableResources: RCX=%p, RDX=%p\n', @rcx, @rdx; .printf 'Stack: '; dps @rsp L8; g"
    
    $$ Hook return addresses to see return values
    bp claude+0x3AC730+0x50 ".printf 'IsEmbeddedAsarIntegrityValidationEnabled returns: RAX=%p\n', @rax; g"
}

$$ Function to monitor memory allocations for ASAR data
.block
{
    .echo "=== ASAR Memory Allocation Monitoring ==="
    
    $$ Hook heap allocations
    bp ntdll!RtlAllocateHeap ".if (@r8 > 0x1000) { .printf 'Large allocation: size=0x%x\n', @r8; k; } g"
    
    $$ Hook VirtualAlloc
    bp kernel32!VirtualAlloc ".if (@rdx > 0x1000) { .printf 'VirtualAlloc: size=0x%x, type=0x%x\n', @rdx, @r9; k; } g"
}

$$ Function to dump crypto operations
.block
{
    .echo "=== Crypto Operations Monitoring ==="
    
    $$ Hook SHA256 operations
    bp bcrypt!BCryptCreateHash ".printf 'BCryptCreateHash: alg=%p\n', @rdx; du @rdx; g"
    bp bcrypt!BCryptHashData ".printf 'BCryptHashData: data=%p, size=0x%x\n', @r8, @r9; db @r8 L20; g"
    bp bcrypt!BCryptFinishHash ".printf 'BCryptFinishHash: hash output=%p, size=0x%x\n', @r8, @r9; g"
}

.echo ""
.echo "Script loaded successfully!"
.echo "Use 'g' to start execution and begin ASAR analysis."
.echo ""
.echo "Advanced commands:"
.echo "  !analyze -v       - Detailed crash analysis if needed"
.echo "  !peb              - Show Process Environment Block"
.echo "  !teb              - Show Thread Environment Block"
.echo "  !heap -s          - Heap summary"
.echo "  !handle           - Show process handles"
.echo ""
.echo "To save analysis results:"
.echo "  .logopen c:\\temp\\asar_analysis.log"
.echo "  .logclose"
